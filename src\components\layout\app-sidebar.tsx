import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>barHeader,
  SidebarRail,
} from '@/components/ui/sidebar'
import { NavGroup } from '@/components/layout/nav-group'
import { NavUser } from '@/components/layout/nav-user'
import { TeamSwitcher } from '@/components/layout/team-switcher'
import { useLanguage } from '@/context/language-context.tsx'
import { getSidebarData } from '@/components/layout/data/sidebar-data.ts'
import { useCurrentUser, useUserProjects } from '@/hooks/user-query.tsx'
import { Skeleton } from '@/components/ui/skeleton.tsx'


export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { t } = useLanguage()
  const sidebarData = getSidebarData(t)
  const { data: user, isLoading, isError } = useCurrentUser()
  const { data: projects, isLoading: isLoadingProjects, isError: isErrorProjects } = useUserProjects()

  return (
    <Sidebar collapsible="icon" variant="floating" {...props}>
      <SidebarHeader>
        {
          (isLoadingProjects || isErrorProjects) &&
          <div className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[150px]" />
            </div>
          </div>
        }
        {projects && projects.length > 0 ? <TeamSwitcher teams={projects} /> : <div />}
      </SidebarHeader>
      <SidebarContent>
        {sidebarData.navGroups.map((props) => (
          <NavGroup key={props.title} {...props} />
        ))}
      </SidebarContent>
      <SidebarFooter>
        {(isLoading || isError) &&
          <div className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[150px]" />
            </div>
          </div>
        }
        {user ? <NavUser user={user} /> : <div />}
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
