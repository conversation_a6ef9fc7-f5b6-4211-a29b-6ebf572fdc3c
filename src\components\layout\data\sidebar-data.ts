import {
  // IconBrandGithubCopilot,
  IconBrowserCheck,
  // IconChecklist,
  IconHelp,
  IconLayoutDashboard,
  // IconMessages,
  IconNotification,
  IconPackages,
  IconPalette,
  IconSettings,
  IconTool,
  IconUserCog,
  // IconUsers,
  IconSitemap,
} from '@tabler/icons-react'
import { AudioWaveform, GalleryVerticalEnd } from 'lucide-react'
import { FaP } from "react-icons/fa6";
import { type SidebarData } from '../types'
import { LocaleType } from '@/locales/cn.ts'

export const getSidebarData = (t: LocaleType): SidebarData => {
  return {
    user: {
    name: 'aisecops-admin',
    email: '<EMAIL>',
    avatar: '/avatars/shadcn.jpg',
  },
  projects: [
    {
      name: 'Project1',
      logo: FaP,
      description: 'Gitlab + Ezone',
    },
    {
      name: 'Acme Inc',
      logo: GalleryVerticalEnd,
      description: 'Enterprise',
    },
    {
      name: 'Acme Corp.',
      logo: AudioWaveform,
      description: 'Startup',
    },
  ],
  navGroups: [
    {
      title: t.Sidebar.general,
      items: [
        {
          title: t.Sidebar.dashboard,
          url: '/dashboard',
          icon: IconLayoutDashboard,
        },
        // {
        //   title: t.Sidebar.tasks,
        //   url: '/tasks',
        //   icon: IconChecklist,
        // },
        {
          title: t.Sidebar.app,
          url: '/apps',
          icon: IconPackages,
        },
        {
          title: t.Sidebar.deploymentPlan,
          url: '/deployment-plan',
          icon: IconSitemap,
        },
        // {
        //   title: t.Sidebar.chat,
        //   url: '/chats',
        //   badge: '3',
        //   icon: IconMessages,
        // },
        // {
        //   title: t.Sidebar.ai,
        //   url: '/ai',
        //   icon: IconBrandGithubCopilot,
        // },
        // {
        //   title: t.Sidebar.user,
        //   url: '/users',
        //   icon: IconUsers,
        // },
      ],
    },
    // {
    //   title: 'Pages',
    //   items: [
    //     {
    //       title: 'Auth',
    //       icon: IconLockAccess,
    //       items: [
    //         {
    //           title: 'Sign In',
    //           url: '/sign-in',
    //         },
    //         {
    //           title: 'Sign Up',
    //           url: '/sign-up',
    //         },
    //         {
    //           title: 'Forgot Password',
    //           url: '/forgot-password',
    //         },
    //         {
    //           title: 'OTP',
    //           url: '/otp',
    //         },
    //       ],
    //     },
    //     {
    //       title: 'Errors',
    //       icon: IconBug,
    //       items: [
    //         {
    //           title: 'Unauthorized',
    //           url: '/401',
    //           icon: IconLock,
    //         },
    //         {
    //           title: 'Forbidden',
    //           url: '/403',
    //           icon: IconUserOff,
    //         },
    //         {
    //           title: 'Not Found',
    //           url: '/404',
    //           icon: IconError404,
    //         },
    //         {
    //           title: 'Internal Server Error',
    //           url: '/500',
    //           icon: IconServerOff,
    //         },
    //         {
    //           title: 'Maintenance Error',
    //           url: '/503',
    //           icon: IconBarrierBlock,
    //         },
    //       ],
    //     },
    //   ],
    // },
    {
      title: t.Sidebar.other,
      items: [
        {
          title: t.Sidebar.settings,
          icon: IconSettings,
          items: [
            {
              title: t.Sidebar.profile,
              url: '/settings',
              icon: IconUserCog,
            },
            {
              title: t.Sidebar.account,
              url: '/settings/account',
              icon: IconTool,
            },
            {
              title: t.Sidebar.appearance,
              url: '/settings/appearance',
              icon: IconPalette,
            },
            {
              title: t.Sidebar.notifications,
              url: '/settings/notifications',
              icon: IconNotification,
            },
            {
              title: t.Sidebar.display,
              url: '/settings/display',
              icon: IconBrowserCheck,
            },
          ],
        },
        {
          title: t.Sidebar.helpCenter,
          url: '/help-center',
          icon: IconHelp,
        },
      ],
    },
  ]
  }
}
