import { useMutation, useQuery } from '@tanstack/react-query'
import axios from '@/lib/axios'
import { DeploymentPlanData } from '@/features/deployment-plan/types'
import { App } from '@/features/apps/data/schema'

/**
 * 获取部署方案列表
 */
export function useDeploymentPlans() {
  return useQuery({
    queryKey: ['deployment-plans'],
    queryFn: () => axios.get<string[]>('/deployment-plans').then(res => res.data),
  })
}

/**
 * 获取特定部署方案
 */
export function useDeploymentPlan(id: string = 'default') {
  return useQuery({
    queryKey: ['deployment-plan', id],
    queryFn: async () => {
      const response = await axios.get<DeploymentPlanData>(`/deployment-plans/${id}`)
      return response.data
    },
  })
}

/**
 * 保存部署方案
 */
export function useSaveDeploymentPlan() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: DeploymentPlanData }) =>
      axios.post(`/deployment-plans/${id}`, data).then(res => res.data),
  })
}

/**
 * 获取可用于部署方案的应用列表
 */
export function useDeploymentPlanApps() {
  return useQuery({
    queryKey: ['deployment-plan-apps'],
    queryFn: async () => {
      const response = await axios.get<App[]>('/deployment-plans/apps')
      return response.data
    },
    // 确保返回空数组而不是 undefined
    select: (data) => data || [],
  })
}
