/* 连接点显示和隐藏样式 */

/* 默认隐藏所有连接点 */
.react-flow__handle {
  opacity: 0 !important;
  transform: scale(0) !important;
  transition: opacity 0.3s ease, transform 0.3s ease !important;
  width: 12px !important;
  height: 12px !important;
  background-color: rgba(0, 0, 0, 0.7) !important;
  border: 2px solid white !important;
}

/* 当鼠标悬停在节点上时显示连接点 */
.handle-container:hover .react-flow__handle,
.react-flow__node.selected .react-flow__handle {
  opacity: 1 !important;
  transform: scale(1) !important;
}

/* 当鼠标悬停在连接点上时放大连接点 */
.react-flow__handle:hover {
  transform: scale(1.5) !important;
  box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.2) !important;
  z-index: 1000 !important;
}

/* 确保连接点在拖动时保持可见 */
.react-flow.dragging .react-flow__handle {
  opacity: 1 !important;
  transform: scale(1.5) !important;
  box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.3) !important;
  z-index: 1000 !important;
}

/* 确保连接点在拖动时保持可见 */
.react-flow.dragging .react-flow__handle:hover {
  transform: scale(1.8) !important;
  box-shadow: 0 0 0 6px rgba(0, 0, 0, 0.4) !important;
}

/* 所有连接点使用黑色带透明度 */
.top-handle,
.bottom-handle,
.left-handle,
.right-handle {
  background-color: rgba(0, 0, 0, 0.7) !important;
}
