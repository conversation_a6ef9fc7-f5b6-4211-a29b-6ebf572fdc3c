/* 连线模式样式 */

/* 连线模式下的光标 */
.connect-mode,
.connect-mode .react-flow__pane,
.connect-mode .react-flow__renderer {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none" stroke="black" stroke-width="2.5"><path d="M16 2v28M2 16h28"/></svg>') 16 16, crosshair !important;
}

/* 连线模式下节点的光标 */
.connect-mode .react-flow__node {
  cursor: pointer !important;
}

/* 连线模式下节点悬停时的样式 */
.connect-mode .react-flow__node:hover {
  filter: brightness(1.1) !important;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.2) !important;
}

/* 连线模式下已选择起点的节点样式 */
.connect-mode .react-flow__node.connection-source {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5) !important;
  filter: brightness(1.1) !important;
}

/* 连线模式下的连接线样式 */
.connect-mode .react-flow__edge .react-flow__edge-path {
  stroke-width: 2.5 !important;
}

/* 连线模式下的连接点样式 - 始终显示 */
.connect-mode .react-flow__handle {
  opacity: 0.7 !important;
  transform: scale(1) !important;
}

/* 连线模式下的连接点悬停样式 */
.connect-mode .react-flow__handle:hover {
  opacity: 1 !important;
  transform: scale(1.5) !important;
  box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.2) !important;
}
