import { SMART_SELECT_APPS_TEMPLATE, SMART_SELECT_PROMPT_TEMPLATE } from '@/constant.ts'
import { useAppsStore } from '@/stores/appsStore.ts'

export function getSmartSelectSystemPrompt(): string {
  const apps = useAppsStore.getState().apps

  let appsStr = "";

  apps.forEach((i) => {
    appsStr += SMART_SELECT_APPS_TEMPLATE.replace(
      "{{ app_name }}",
      i.name,
    ).replace(
      "{{ app_description }}",
      i.desc
    );
  });

  return SMART_SELECT_PROMPT_TEMPLATE.replace("{{ APPS }}", appsStr);
}