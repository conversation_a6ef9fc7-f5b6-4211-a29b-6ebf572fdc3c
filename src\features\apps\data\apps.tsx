import {
  IconBrandGithub,
  IconBrandDocker,
  IconBrandOpenai,
  IconCode,
  IconDatabase,
  IconBrandChrome,
  IconTools,
  IconJson,
  IconRobot,
  IconMessageChatbot,
  IconMail,
  IconLetterH,
} from '@tabler/icons-react'

// Mock data for apps - this will be moved to the mock handler
export const apps = [
  {
    id: '1',
    name: '<PERSON><PERSON>',
    logo: <IconLetterH />,
    tag: '博客',
    integrated: false,
    desc: '强大易用的开源建站工具。',
  },
  {
    id: '2',
    name: 'Headscale',
    logo: <IconCode />,
    tag: '工具',
    integrated: true,
    desc: '一个自托管的开源 Tailscale 控制服务器实现。',
  },
  {
    id: '3',
    name: 'Headscale PostgreSQL 版',
    logo: <IconDatabase />,
    tag: '工具',
    integrated: true,
    desc: '一个自托管的开源 Tailscale 控制服务器实现，使用 PostgreSQL 作为数据库。',
  },
  {
    id: '4',
    name: 'inpaint-web',
    logo: <IconBrandChrome />,
    tag: '工具',
    integrated: false,
    desc: 'A free and open-source inpainting & image-upscaling tool powered by webgpu and wasm on the browser.',
  },
  {
    id: '5',
    name: 'IT-Tools',
    logo: <IconTools />,
    tag: '工具',
    integrated: false,
    desc: 'Useful tools for developer and people working in IT.',
  },
  {
    id: '6',
    name: 'jsoncrack',
    logo: <IconJson />,
    tag: '工具',
    integrated: true,
    desc: 'JSON Crack is a free, open-source data visualization app capable of visualizing data formats such as JSON, YAML, XML, CSV and more.',
  },
  {
    id: '7',
    name: 'LibreChat',
    logo: <IconMessageChatbot />,
    tag: 'AI',
    integrated: false,
    desc: 'Enhanced ChatGPT Clone',
  },
  {
    id: '8',
    name: 'listmonk',
    logo: <IconMail />,
    tag: '工具',
    integrated: true,
    desc: 'High performance, self-hosted, newsletter and mailing list manager with a modern dashboard. Single binary.',
  },
  {
    id: '9',
    name: 'Llama3-8B 中文版',
    logo: <IconRobot />,
    tag: 'AI',
    integrated: false,
    desc: 'Llama3-8B 中文量化模型，提供了与 OpenAI 官方接口对齐的 API。',
  },
  {
    id: '10',
    name: 'Docker',
    logo: <IconBrandDocker />,
    tag: '工具',
    integrated: false,
    desc: '轻松管理 Docker 容器。',
  },
  {
    id: '11',
    name: 'GitHub',
    logo: <IconBrandGithub />,
    tag: '工具',
    integrated: false,
    desc: '简化 GitHub 代码管理。',
  },
  {
    id: '12',
    name: 'OpenAI',
    logo: <IconBrandOpenai />,
    tag: 'AI',
    integrated: false,
    desc: '集成 OpenAI 的 API 服务。',
  },
  {
    id: '13',
    name: 'MYSQL',
    logo: <IconDatabase />,
    tag: 'DB',
    integrated: false,
    desc: 'MySQL是一款开源、可靠且高性能的关系型数据库管理系统，被广泛应用于各类Web应用和数据存储场景。',
  },
  {
    id: '14',
    name: 'Redis',
    logo: <IconDatabase />,
    tag: 'DB',
    integrated: false,
    desc: '一款开源的高性能键值对内存数据库，具备丰富的数据结构，支持持久化，常用于缓存、消息队列等场景以提升系统响应速度和并发处理能力。',
  },
  {
    id: '15',
    name: 'MongoDB',
    logo: <IconDatabase />,
    tag: 'DB',
    integrated: false,
    desc: 'MongoDB是一种开源的、面向文档的NoSQL数据库，以其灵活的数据模型和强大的查询功能而闻名。',
  }
]
