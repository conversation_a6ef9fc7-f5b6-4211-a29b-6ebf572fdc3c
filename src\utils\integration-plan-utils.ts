import { App } from '@/features/apps/data/schema'
import { Node } from '@xyflow/react'

/**
 * 将应用添加到部署方案中
 * @param app 要添加的应用
 * @param t 翻译对象
 * @returns 是否成功添加
 */
export const addAppToDeploymentPlan = (app: App) => {
  try {
    // 获取当前部署方案数据
    const deploymentPlanData = localStorage.getItem('deployment-plan-data')
    let planData = deploymentPlanData ? JSON.parse(deploymentPlanData) : { nodes: [], edges: [] }

    // 计算新节点的位置
    const existingNodes = planData.nodes || []
    const position = {
      x: 100 + Math.random() * 100,
      y: 100 + existingNodes.length * 80 + Math.random() * 50
    }

    // 确保 logo 是字符串
    const iconId = typeof app.logo === 'string' ? app.logo : app.id

    // 创建新节点
    const newNode: Node = {
      id: `app-${app.id}-${Date.now()}`,
      type: 'custom',
      position,
      data: {
        label: app.name,
        description: app.desc,
        icon: iconId,
        tag: app.tag,
      },
    }

    // 添加新节点
    planData.nodes = [...existingNodes, newNode]

    // 保存部署方案数据
    localStorage.setItem('deployment-plan-data', JSON.stringify(planData))

    // 返回成功
    return true
  } catch (error) {
    return false
  }
}
