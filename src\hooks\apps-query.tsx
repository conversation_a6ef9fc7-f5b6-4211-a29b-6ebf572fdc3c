import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { getApps, updateAppConnection } from '@/api/apps-api'
import { toast } from 'sonner'
import { useAppsStore } from '@/stores/appsStore'

/**
 * Hook to fetch all apps
 */
export function useApps() {
  const setApps = useAppsStore((state) => state.setApps)

  return useQuery({
    queryKey: ['apps'],
    queryFn: () => getApps().then(res => {
      setApps(res.data)
      return res.data
    }),
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnMount: true
  })
}

/**
 * Hook to update app integration status
 */
export function useUpdateAppConnection() {
  const queryClient = useQueryClient()
  const updateApp = useAppsStore((state) => state.updateApp)

  return useMutation({
    mutationFn: ({ id, integrated }: { id: string; integrated: boolean }) =>
      updateAppConnection(id, integrated),
    onSuccess: (response, variables) => {
      // 确保响应数据符合预期
      if (response.data) {
        updateApp(variables.id, { integrated: variables.integrated })
        queryClient.invalidateQueries({ queryKey: ['apps'] })
        toast.success('App integration status updated successfully')
      } else {
        toast.error('Invalid response from server')
      }
    },
    onError: (error) => {
      console.error('Error updating app integration status:', error)
      toast.error('Failed to update app integration status')
    }
  })
}
