import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import {
  IconBrandGithubCopilot,
  IconMessageCirclePlus,
  IconSend,
  IconTrash,
} from '@tabler/icons-react'
import React, { Fragment, useEffect, useRef, useState } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeHighlight from 'rehype-highlight'

import 'highlight.js/styles/github-dark.css'
import { SmartSelectMessagesProps } from '@/features/apps/data/type.ts'
import { ChatInputProps } from '@/services/type.ts'
import { useLanguage } from '@/context/language-context.tsx'
import { Textarea } from '@/components/ui/textarea.tsx'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip.tsx'
import { Button } from '@/components/ui/button.tsx'
import { ChatMessage, createMessage, useChatStore } from '@/stores/aiStore.ts'
import { v4 as uuidv4 } from 'uuid'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

import markdownComponents from '@/components/markdown.tsx'
import { cn } from '@/lib/utils.ts'
import { PanelRightIcon } from 'lucide-react'
import { Separator } from '@/components/ui/separator.tsx'

const type = 'smartSelect'

export default function SmartSelect({ fallback, close }: { fallback: (list: string[]) => void, close: () => void }) {
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const chatStore = useChatStore()
  const sessions = chatStore.sessions.filter(session => session.type === 'smartSelect')
  const [session, setSession] = useState(chatStore.getCurrentSession('smartSelect'))
  const createNewSession = () => {
    const date = new Date()
    const newSession = {
      id: `smartSelect-${uuidv4()}`,
      topic: `最近会话-${date.toLocaleString()}`,
      createdAt: date,
      messages: [createMessage({
        role: 'apiMessage',
        content: '你好，我是智选助手，我可以根据你的需求，帮助你选择合适的应用!',
      })],
      type: 'smartSelect' as const,
    }
    setSession(newSession)
    chatStore.addSession(newSession)
  }
  if (!session) {
    createNewSession()
  }
  const [messages, setMessages] = useState<ChatMessage[]>(session?.messages || [])

  useEffect(() => {
    if (session) {
      setMessages(session.messages)
    }
  }, [session?.messages.length, session])

  const handleSend = async () => {
    if (inputValue.trim() === '' || isLoading) return
    setIsLoading(true)
    chatStore.onSmartSelectInput(inputValue).then(() => setIsLoading(false))
    setInputValue('')
  }

  const handleSelect = (sessionID: string) => {
    chatStore.setCurrentSession(sessionID, type)
    setSession(chatStore.getCurrentSession(type))
  }

  const deleteSession = () => {
    const currentSessionId = chatStore.currentSmartSelectSessionId || ''
    const newSession = chatStore.deleteSession(currentSessionId, type)?.[0]
    if (newSession) {
      setSession(newSession)
      chatStore.setCurrentSession(newSession.id, type)
    } else {
      createNewSession()
    }
  }

  const closeSmartSelection = () => {
    close()
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="border-b px-2 [.border-b]:pb-2">
        <div className="flex justify-between items-center">
          <div className="flex gap-2">
            <div className="flex justify-center items-center gap-2">
              <Button
                data-sidebar="trigger"
                data-slot="sidebar-trigger"
                variant="outline"
                size="icon"
                className={cn('size-7', 'scale-125 sm:scale-100')}
                onClick={closeSmartSelection}
              >
                <PanelRightIcon />
                <span className="sr-only">Toggle Sidebar</span>
              </Button>
              <Separator orientation="vertical" className="h-6" />
            </div>
            <Select value={session?.id} onValueChange={handleSelect}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select a session" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  {sessions.map((item => {
                    return (
                      <SelectItem value={item.id} key={item.id}>{item.topic}</SelectItem>
                    )
                  }))}
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          <div className="flex">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button size="icon" variant="ghost" onClick={createNewSession}>
                    <IconMessageCirclePlus size={20} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>新建会话</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button size="icon" variant="ghost" onClick={deleteSession}>
                    <IconTrash size={20} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>删除当前会话</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
        <ChatContent messages={messages} fallback={fallback} />
      </CardContent>

      <div className="px-4 pt-4 border-t">
        <div className="relative">
          <ChatInput
            disabled={isLoading}
            value={inputValue}
            onSend={handleSend}
            onValueChange={setInputValue}
          />
        </div>
      </div>
    </Card>
  )
}

const LoadingDots = () => {
  const [dots, setDots] = React.useState('')

  useEffect(() => {
    const interval = setInterval(() => {
      setDots((prev) => (prev.length >= 4 ? '' : prev + '.'))
    }, 500)

    return () => clearInterval(interval)
  }, [])

  return (
    <span className="inline-block min-w-[24px] text-xl font-bold">{dots}</span>
  )
}

function ChatContent({ messages, fallback }: SmartSelectMessagesProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [appsList, setAppsList] = useState<string[]>([])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const lastMessageContent = messages[messages.length - 1]?.content;

  useEffect(() => {
    scrollToBottom()
  }, [messages, lastMessageContent])

  useEffect(() => {
    if (appsList.length > 0) {
      fallback(appsList)
    }
  }, [appsList, fallback])


  return (
    <div className="flex-1 overflow-y-auto p-2 overflow-x-hidden">
      <div className="max-w-3xl mx-auto">
        <div className="flex flex-col space-y-4">
          {messages.map((message) => {
            if (message.status === 'success' && !message.smartSelectHandle) {
              message.smartSelectHandle = true
              setAppsList(message.smartSelectList!)
            }
            return (
              <Fragment key={message.id}>
                <div
                  key={message.id}
                  className={`flex max-w-full`}
                >
                  {message.role === 'apiMessage' && (
                    <div
                      className={`min-w-8 max-w-8 h-8 rounded-full flex items-center justify-center mr-2 border`}
                    >
                      <IconBrandGithubCopilot />
                    </div>
                  )}
                  {message.role === 'userMessage' && (
                    <div
                      className={`min-w-8 max-w-8 h-8 rounded-full flex items-center justify-center mr-2 border text-green-500`}
                    >
                      U
                    </div>
                  )}
                  <div
                    className={`rounded-lg p-3 break-words ${
                      message.role === 'userMessage'
                        ? 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200'
                        : 'prose dark:prose-invert max-w-none overflow-hidden'
                    }`}
                  >
                    {message.content ? (
                      message.role === 'apiMessage' ? (
                        <Fragment>
                          <ReactMarkdown
                            components={markdownComponents}
                            rehypePlugins={[rehypeHighlight]}
                            remarkPlugins={[remarkGfm]}
                          >
                            {message.content}
                          </ReactMarkdown>
                        </Fragment>
                      ) : (
                        message.content
                      )
                    ) : message.role === 'apiMessage' &&
                    messages[messages.length - 1].id === message.id ? (
                      <LoadingDots />
                    ) : (
                      ''
                    )}
                  </div>
                </div>
              </Fragment>
            )
          })}
          <div ref={messagesEndRef} />
        </div>
      </div>
    </div>
  )
}

function ChatInput({
                     value,
                     onValueChange,
                     onSend,
                     disabled,
                   }: ChatInputProps) {

  const { t } = useLanguage()
  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLTextAreaElement>,
  ) => {
    if (e.key === 'Enter') {
      if (e.ctrlKey) {
        // Ctrl+Enter: Add new line
        onValueChange(value + '\n')
      } else {
        // Enter: Send message
        e.preventDefault()
        onSend()
      }
    }
  }

  return (
    <div className="w-full">
      <div className="flex flex-col rounded-xl p-3 border shadow-sm bg-background">
        <div className="flex items-center">
          <Textarea
            className="border-none shadow-none resize-none outline-none flex-1 min-h-[40px] max-h-[60px] py-1 px-2 bg-background text-foreground pt-3 focus:ring-0 focus:border-transparent focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none"
            disabled={disabled}
            placeholder={"发送消息给智选助手"}
            value={value}
            onChange={(e) => onValueChange(e.target.value)}
            onKeyDown={e => handleKeyDown(e)}
            rows={1}
          />
          <div className="flex items-center justify-end ml-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="icon"
                    aria-label="发送"
                    color="primary"
                    disabled={disabled}
                    onClick={onSend}
                    className="h-8 w-8"
                  >
                    <IconSend size={20} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {t.ai.send}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>
    </div>
  )
}