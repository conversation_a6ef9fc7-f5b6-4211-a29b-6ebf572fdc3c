import axiosInstance from '@/lib/axios'
import { App } from '@/features/apps/data/schema'

/**
 * Get all apps
 * @returns Promise with apps data
 */
export const getApps = async () => {
  return await axiosInstance.get<App[]>('/apps/getApps')
}

/**
 * Update app integration status
 * @param id App ID
 * @param integrated New integration status
 * @returns Promise with updated app data
 */
export const updateAppConnection = async (id: string, integrated: boolean) => {
  return await axiosInstance.post<App>('/apps/updateConnection', { id, integrated })
}
