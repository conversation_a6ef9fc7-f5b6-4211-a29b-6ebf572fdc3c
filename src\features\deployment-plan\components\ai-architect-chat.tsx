import { useState, Fragment, useRef, useEffect } from 'react'
import { IconChartBar } from '@tabler/icons-react'
import { Loader2 } from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeHighlight from 'rehype-highlight'
import "highlight.js/styles/github-dark.css";
import PanelLayout from './shared/panel-layout';

// Define the props for the AI Architect Chat component
interface AIArchitectChatProps {
  isOpen: boolean;
  onClose: () => void;
}

// Define the message type
interface ArchitectMessage {
  id: string;
  content: string;
  role: 'assistant';
}

// Markdown components for rendering
const markdownComponents = {
  pre: ({ node, ...props }: any) => (
    <div className="overflow-auto w-full my-2 bg-gray-800 dark:bg-gray-900 p-2 rounded-lg">
      <pre {...props} />
    </div>
  ),
  code: ({ node, className, children, ...props }: any) => {
    const match = /language-(\w+)/.exec(className || "");
    return match ? (
      <code className={className} {...props}>
        {children}
      </code>
    ) : (
      <code className="bg-gray-200 dark:bg-gray-800 px-1 rounded-sm" {...props}>
        {children}
      </code>
    );
  },
};

// Loading dots animation component
const LoadingDots = () => {
  const [dots, setDots] = useState('')

  useEffect(() => {
    const interval = setInterval(() => {
      setDots((prev) => (prev.length >= 4 ? '' : prev + '.'))
    }, 500)

    return () => clearInterval(interval)
  }, [])

  return (
    <span className="inline-block min-w-[24px] text-xl font-bold">{dots}</span>
  )
}

export default function AIArchitectChat({ isOpen, onClose }: AIArchitectChatProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [messages, setMessages] = useState<ArchitectMessage[]>([
    {
      id: '1',
      content: '你好，我是 AI 架构师，我可以帮助你评估当前的部署架构。点击"架构评分"按钮，我将对你的部署方案进行分析和评分。',
      role: 'assistant'
    }
  ])

  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages, messages[messages.length - 1]?.content])

  // 在小屏幕上自动关闭聊天框
  useEffect(() => {
    const handleResize = () => {
      if (isOpen && window.innerWidth < 1024) {
        onClose()
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [isOpen, onClose])

  const handleArchitectureScore = () => {
    setIsLoading(true)

    // Simulate API call with timeout
    setTimeout(() => {
      const newMessage: ArchitectMessage = {
        id: Date.now().toString(),
        content: `## 架构评分: 85/100

### 优势
- 数据库与应用服务分离，符合最佳实践
- 使用了合适的技术栈组合
- 系统结构清晰

### 改进建议
- 考虑添加负载均衡器提高可用性
- 建议增加监控组件
- 可以考虑引入缓存层提升性能

### 详细分析
当前架构采用了三层结构，基本满足需求。建议考虑引入更多的安全措施，例如WAF和入侵检测系统。

数据流向合理，但缺少数据备份和恢复策略。建议添加定期备份机制和灾难恢复计划。`,
        role: 'assistant'
      }

      setMessages(prev => [...prev, newMessage])
      setIsLoading(false)
    }, 2000)
  }

  if (!isOpen) return null

  const chatContent = (
    <div className="overflow-y-auto overflow-x-hidden h-full">
      <div className="mx-auto">
        <div className="flex flex-col space-y-3">
          {messages.map((message) => (
            <Fragment key={message.id}>
              <div className="flex max-w-full">
                <div className="min-w-8 max-w-8 h-8 rounded-full flex items-center justify-center mr-2 border bg-primary/10 text-primary">
                  <span className="text-xs font-bold">AI</span>
                </div>
                <div className="rounded-lg p-3 break-words prose dark:prose-invert max-w-none overflow-hidden">
                  {message.content ? (
                    <ReactMarkdown
                      components={markdownComponents}
                      rehypePlugins={[rehypeHighlight]}
                      remarkPlugins={[remarkGfm]}
                    >
                      {message.content}
                    </ReactMarkdown>
                  ) : (
                    <LoadingDots />
                  )}
                </div>
              </div>
            </Fragment>
          ))}
          {isLoading && (
            <div className="flex max-w-full">
              <div className="min-w-8 max-w-8 h-8 rounded-full flex items-center justify-center mr-2 border bg-primary/10 text-primary">
                <span className="text-xs font-bold">AI</span>
              </div>
              <div className="rounded-lg p-3 break-words">
                <LoadingDots />
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>
    </div>
  )

  const footerButton = (
    <button
      onClick={isLoading ? undefined : handleArchitectureScore}
      className={`h-9 w-64 text-base rounded-md flex items-center justify-center gap-2 transition-all duration-200 ease-out ${
        isLoading
          ? 'bg-gray-600 dark:bg-gray-700 text-white circle-off-cursor'
          : 'bg-primary text-primary-foreground hover:bg-primary/90'
      }`}
    >
      {isLoading ? (
        <>
          <Loader2 size={18} className="mr-2 animate-spin" />
          评分中...
        </>
      ) : (
        <>
          <IconChartBar size={18} className="mr-2" />
          架构评分
        </>
      )}
    </button>
  )

  return (
    <PanelLayout
      title="AI 架构师"
      onClose={onClose}
      footer={footerButton}
    >
      {chatContent}
    </PanelLayout>
  )
}




























