// 应用市场数据


interface appMarketItem  {
  name: string
  icon: string

}




export const getAppMarketData=():appMarketItem[] => {
  return [
    { name: "Unbounce", icon: "src/features/landing/data/unbounce.png" },
    { name: 'Kitmailer', icon: 'src/features/landing/data/typefully.png' },
    { name: 'Rise', icon: 'src/features/landing/data/rise.png' },
    { name: 'ClickUp', icon: 'src/features/landing/data/clickUp.png' },
    { name: '<PERSON><PERSON>', icon: 'src/features/landing/data/sunsama.png' },
    { name: '<PERSON><PERSON>', icon: 'src/features/landing/data/morgen.png' },
    { name: "<PERSON><PERSON>", icon: "src/features/landing/data/unbounce.png" },
    { name: 'Beeper', icon: 'src/features/landing/data/typefully.png' },
    { name: 'Voideflow', icon: 'src/features/landing/data/rise.png' },
    { name: 'Slite', icon: 'src/features/landing/data/clickUp.png' },
    { name: '<PERSON>bash', icon: 'src/features/landing/data/sunsama.png' },
    { name: 'Verbu', icon: 'src/features/landing/data/morgen.png' },
    { name: "Typefully", icon: "src/features/landing/data/unbounce.png" },
    { name: 'Bento', icon: 'src/features/landing/data/typefully.png' },
    { name: 'Moises', icon: 'src/features/landing/data/rise.png' },
    { name: 'Kosy', icon: 'src/features/landing/data/clickUp.png' },
    { name: 'Cal', icon: 'src/features/landing/data/sunsama.png' },
    { name: 'LifeAt', icon: 'src/features/landing/data/morgen.png' }
  ]
}