import { useState, useEffect } from 'react'
import { Node } from '@xyflow/react'
import { IconDeviceFloppy } from '@tabler/icons-react'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { getIconById, getIconColor } from '@/features/apps/utils/icon-utils'
import { HostNode } from '../types'
import PanelLayout from './shared/panel-layout'

// 定义主机节点编辑面板的属性
interface HostNodeEditPanelProps {
  isOpen: boolean;
  onClose: () => void;
  selectedNode: Node | null;
  onSave: (updatedData: {
    label: string;
    description: string;
    ipAddress: string;
    operatingSystem: string;
    diskSpace: string;
    memory: string;
    cpuCores: string;
  }) => void;
}

export default function HostNodeEditPanel({ isOpen, onClose, selectedNode, onSave }: HostNodeEditPanelProps) {
  const [nodeName, setNodeName] = useState('')
  const [nodeDescription, setNodeDescription] = useState('')
  const [ipAddress, setIpAddress] = useState('')
  const [operatingSystem, setOperatingSystem] = useState('')
  const [diskSpace, setDiskSpace] = useState('')
  const [memory, setMemory] = useState('')
  const [cpuCores, setCpuCores] = useState('')

  // 保存原始值以检测变化
  const [originalNodeName, setOriginalNodeName] = useState('')
  const [originalNodeDescription, setOriginalNodeDescription] = useState('')
  const [originalIpAddress, setOriginalIpAddress] = useState('')
  const [originalOperatingSystem, setOriginalOperatingSystem] = useState('')
  const [originalDiskSpace, setOriginalDiskSpace] = useState('')
  const [originalMemory, setOriginalMemory] = useState('')
  const [originalCpuCores, setOriginalCpuCores] = useState('')
  const [hasChanges, setHasChanges] = useState(false)

  // 当选中节点变化时更新表单值
  useEffect(() => {
    if (selectedNode) {
      // 使用类型断言确保 data 符合预期的类型
      const nodeData = selectedNode.data as HostNode['data'];

      // 设置当前值
      setNodeName(nodeData.label || '')
      setNodeDescription(nodeData.description || '')
      setIpAddress(nodeData.ipAddress || '')
      setOperatingSystem(nodeData.operatingSystem || '')
      setDiskSpace(nodeData.diskSpace || '')
      setMemory(nodeData.memory || '')
      setCpuCores(nodeData.cpuCores || '')

      // 保存原始值
      setOriginalNodeName(nodeData.label || '')
      setOriginalNodeDescription(nodeData.description || '')
      setOriginalIpAddress(nodeData.ipAddress || '')
      setOriginalOperatingSystem(nodeData.operatingSystem || '')
      setOriginalDiskSpace(nodeData.diskSpace || '')
      setOriginalMemory(nodeData.memory || '')
      setOriginalCpuCores(nodeData.cpuCores || '')

      // 重置变化状态
      setHasChanges(false)
    }
  }, [selectedNode])

  // 检测内容变化
  useEffect(() => {
    if (selectedNode) {
      setHasChanges(
        nodeName !== originalNodeName ||
        nodeDescription !== originalNodeDescription ||
        ipAddress !== originalIpAddress ||
        operatingSystem !== originalOperatingSystem ||
        diskSpace !== originalDiskSpace ||
        memory !== originalMemory ||
        cpuCores !== originalCpuCores
      )
    }
  }, [
    nodeName, nodeDescription, ipAddress, operatingSystem, diskSpace, memory, cpuCores,
    originalNodeName, originalNodeDescription, originalIpAddress, originalOperatingSystem,
    originalDiskSpace, originalMemory, originalCpuCores,
    selectedNode
  ])

  // 处理保存
  const handleSave = () => {
    if (selectedNode) {
      // 验证必填字段
      if (!ipAddress.trim()) {
        alert('请输入IP地址');
        return;
      }

      if (!nodeName.trim()) {
        alert('请输入主机名称');
        return;
      }

      onSave({
        label: ipAddress, // 使用IP地址作为节点名称
        description: nodeName, // 使用主机名称作为描述
        ipAddress,
        operatingSystem,
        diskSpace,
        memory,
        cpuCores
      })

      // 更新原始值，重置变化状态
      setOriginalNodeName(nodeName)
      setOriginalNodeDescription(nodeDescription)
      setOriginalIpAddress(ipAddress)
      setOriginalOperatingSystem(operatingSystem)
      setOriginalDiskSpace(diskSpace)
      setOriginalMemory(memory)
      setOriginalCpuCores(cpuCores)
      setHasChanges(false)
    }
  }

  // 如果面板未打开或没有选中节点，则不显示
  if (!isOpen || !selectedNode) return null

  const hostNodeContent = (
    <>
      <div className="flex items-center mb-4">
        {/* 使用类型断言确保 data 符合预期的类型 */}
        {(() => {
          const nodeData = selectedNode.data as { icon?: string; tag?: string };
          return (
            <>
              <div className="flex size-10 items-center justify-center rounded-lg p-2 bg-gray-50 dark:bg-gray-700 flex-shrink-0">
                <div className={`text-2xl ${getIconColor(nodeData.icon || '1')}`}>
                  {getIconById(nodeData.icon || '1')}
                </div>
              </div>
              {nodeData.tag && (
                <div className="ml-3 text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-md inline-block">
                  {nodeData.tag}
                </div>
              )}
            </>
          );
        })()}
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="nodeName">主机名称 <span className="text-red-500">*</span></Label>
          <Input
            id="nodeName"
            value={nodeName}
            onChange={(e) => setNodeName(e.target.value)}
            placeholder="输入主机名称"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="ipAddress">IP地址 <span className="text-red-500">*</span></Label>
          <Input
            id="ipAddress"
            value={ipAddress}
            onChange={(e) => setIpAddress(e.target.value)}
            placeholder="例如: *************"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="operatingSystem">操作系统</Label>
          <Input
            id="operatingSystem"
            value={operatingSystem}
            onChange={(e) => setOperatingSystem(e.target.value)}
            placeholder="例如: Windows Server 2022, Ubuntu 22.04"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="cpuCores">CPU核心数</Label>
          <div className="flex items-center">
            <Input
              id="cpuCores"
              value={cpuCores}
              onChange={(e) => setCpuCores(e.target.value)}
              placeholder="例如: 4"
              className="flex-1"
            />
            <span className="ml-2 text-sm text-gray-500">核</span>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="memory">内存容量</Label>
          <div className="flex items-center">
            <Input
              id="memory"
              value={memory}
              onChange={(e) => setMemory(e.target.value)}
              placeholder="例如: 16"
              className="flex-1"
            />
            <span className="ml-2 text-sm text-gray-500">GB</span>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="diskSpace">磁盘容量</Label>
          <div className="flex items-center">
            <Input
              id="diskSpace"
              value={diskSpace}
              onChange={(e) => setDiskSpace(e.target.value)}
              placeholder="例如: 500"
              className="flex-1"
            />
            <span className="ml-2 text-sm text-gray-500">GB</span>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="nodeDescription">备注说明</Label>
          <Textarea
            id="nodeDescription"
            value={nodeDescription}
            onChange={(e) => setNodeDescription(e.target.value)}
            placeholder="输入主机描述（可选）"
            rows={4}
          />
        </div>
      </div>
    </>
  )

  const saveButton = (
    <button
      onClick={handleSave}
      className="h-9 w-64 text-base rounded-md flex items-center justify-center gap-2 transition-all duration-200 ease-out bg-primary text-primary-foreground hover:bg-primary/90"
    >
      <IconDeviceFloppy size={18} className="mr-2" />
      保存主机
    </button>
  )

  return (
    <div id="host-node-edit-panel" data-has-changes={hasChanges.toString()}>
      <PanelLayout
        title="编辑主机节点"
        onClose={onClose}
        footer={saveButton}
      >
        {hostNodeContent}
      </PanelLayout>
    </div>
  )
}
