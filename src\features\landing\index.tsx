import { useNavigate } from '@tanstack/react-router'
import { Button } from '@/components/ui/button'
// import { cn } from "@/lib/utils";
// import { Marquee } from "@/components/magicui/marquee";
import { getAppMarketData } from '@/features/landing/data/landing-data.ts'
import { Logo } from '@/components/icons.tsx'

export default function LandingPage() {
  const navigate = useNavigate()
  const appMarket = getAppMarketData()

  return (
    <>
      {/* ===== Header Navigation ===== */}
      <header className="flex sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex  justify-center items-center ml-5">
          <Logo size={30} />
          <span>AISecOps</span>
        </div>
        <div className="container flex h-14 items-center justify-between">
          <nav className="flex items-center space-x-6">
            {topNav.map((item) => (
              <Button
                key={item.title}
                variant="ghost"
                disabled={item.disabled}
                onClick={() => !item.disabled && navigate({ to: item.href })}
                className={item.isActive ? 'text-primary' : ''}
              >
                {item.title}
              </Button>
            ))}
          </nav>
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={() => navigate({ to: '/sign-in' })}>
              登录
            </Button>
            <Button onClick={() => navigate({ to: '/sign-up' })}>
              注册
            </Button>
          </div>
        </div>
      </header>

      {/* ===== Main Content - 调整了高度和间距 ===== */}
      <div className='min-h-[calc(100svh-56px)] pt-10'>
        <div className='m-auto flex w-full max-w-3xl flex-col items-center justify-start gap-4 px-4'>
          <h1 className='text-6xl font-bold'>AISecOps</h1>
          <span className='text-lg font-medium'>全场景一站式运维平台，AISecOps提升业务价值，价值驱动IT应用运维</span>
          <p className='text-muted-foreground text-center'>
          智慧安全运维（AISecOps）是一套理论+实践体系，包含了"理论+团队+工具"，体系融合了SRE、ITIL、CISSP和DevOps的最佳实践，通过整合AI能力到一体化运维工具为SRE运维团队提供高效决策，保障IT应用运营期间的可靠与安全，为客户持续提供业务价值。
          </p>
        </div>
        {/* ===== 应用市场区块 ===== */}
        <div className="flex flex-col items-center my-12 max-w-4xl m-auto bg-gradient-to-t from-white to-amber-200/10">
          <div className="flex items-center justify-center gap-2 mb-1">
            <svg width="60" height="32" viewBox="0 0 60 32" fill="none" xmlns="http://www.w3.org/2000/svg"
                 className="mt-8">
              <path d="M56 6C40 6 16 6 8 24" stroke="#C0C0C0" strokeWidth="2" fill="none" />
              <path d="M8 24L12 22" stroke="#C0C0C0" strokeWidth="2" strokeLinecap="round" />
              <path d="M8 24L6 20" stroke="#C0C0C0" strokeWidth="2" strokeLinecap="round" />
            </svg>
            <h2 className="text-3xl font-extrabold text-center">丰富的应用市场</h2>
            <svg width="60" height="32" viewBox="0 0 60 32" fill="none" xmlns="http://www.w3.org/2000/svg"
                 className="mt-8">
              <path d="M4 6C20 6 44 6 52 24" stroke="#C0C0C0" strokeWidth="2" fill="none" />
              <path d="M52 24L48 22" stroke="#C0C0C0" strokeWidth="2" strokeLinecap="round" />
              <path d="M52 24L54 20" stroke="#C0C0C0" strokeWidth="2" strokeLinecap="round" />
            </svg>
          </div>
          <span className="text-sm text-muted-foreground mb-5 text-center">一站式集成多种高效工具，助力智能运维</span>
          <div
            className="dark:bg-zinc-900/80 rounded-2xl p-4 max-w-4xl w-auto flex flex-col items-center mb-10">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-7">
              {appMarket.map((app: { name: string; icon: string }) => (
                <div
                  key={app.name}
                  className="flex flex-col items-center justify-center bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-xl p-4 transition-shadow hover:shadow-md hover:bg-zinc-100 dark:hover:bg-zinc-700 cursor-pointer min-w-[90px] min-h-[110px]"
                >
                  <img src={app.icon} alt={app.name} className="w-12 h-12 rounded-lg border shadow mb-3" />
                  <span className="text-[15px] font-semibold text-zinc-800 dark:text-zinc-100 text-center leading-tight">{app.name}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
    {/*    <div className="relative flex w-full flex-col items-center justify-center overflow-hidden mt-16">*/}
    {/*  <Marquee pauseOnHover className="[--duration:20s]">*/}
    {/*    {firstRow.map((review) => (*/}
    {/*      <ReviewCard key={review.username} {...review} />*/}
    {/*    ))}*/}
    {/*  </Marquee>*/}
    {/*  <Marquee reverse pauseOnHover className="[--duration:20s]">*/}
    {/*    {secondRow.map((review) => (*/}
    {/*      <ReviewCard key={review.username} {...review} />*/}
    {/*    ))}*/}
    {/*  </Marquee>*/}
    {/*  <div className="pointer-events-none absolute inset-y-0 left-0 w-1/4 bg-gradient-to-r from-background"></div>*/}
    {/*  <div className="pointer-events-none absolute inset-y-0 right-0 w-1/4 bg-gradient-to-l from-background"></div>*/}
    {/*</div>*/}
      </div>
      
    </>
  )
}

const topNav = [
  {
    title: '首页',
    href: '/',
    isActive: true,
    disabled: false,
  },
  // {
  //   title: '体系介绍',
  //   href: '/docs',
  //   isActive: false,
  //   disabled: true,
  // },
]
// const reviews = [
//   {
//     name: "Mysql",
//     username: "@Mysql",
//     body: "I've never seen anything like this before. It's amazing. I love it.",
//     img: "https://avatar.vercel.sh/Mysql",
//   },
//   {
//     name: "V智",
//     username: "@V智",
//     body: "I don't know what to say. I'm speechless. This is amazing.",
//     img: "https://avatar.vercel.sh/jill",
//   },
//   {
//     name: "Gitlab",
//     username: "@Gitlab",
//     body: "I'm at a loss for words. This is amazing. I love it.",
//     img: "https://avatar.vercel.sh/Gitlab",
//   },
//   {
//     name: "Kubernetes",
//     username: "@Kubernetes",
//     body: "I'm at a loss for words. This is amazing. I love it.",
//     img: "https://avatar.vercel.sh/Kubernetes",
//   },
//   {
//     name: "智博",
//     username: "@智博",
//     body: "I'm at a loss for words. This is amazing. I love it.",
//     img: "https://avatar.vercel.sh/智博",
//   },
//   {
//     name: "鹰智达",
//     username: "@鹰智达",
//     body: "I'm at a loss for words. This is amazing. I love it.",
//     img: "https://avatar.vercel.sh/鹰智达",
//   },
// ];
//
// const firstRow = reviews.slice(0, reviews.length / 2);
// const secondRow = reviews.slice(reviews.length / 2);
//
// const ReviewCard = ({
//   img,
//   name,
//   username,
//   body,
// }: {
//   img: string;
//   name: string;
//   username: string;
//   body: string;
// }) => {
//   return (
//     <figure
//       className={cn(
//         "relative h-full w-64 cursor-pointer overflow-hidden rounded-xl border p-4",
//         // light styles
//         "border-gray-950/[.1] bg-gray-950/[.01] hover:bg-gray-950/[.05]",
//         // dark styles
//         "dark:border-gray-50/[.1] dark:bg-gray-50/[.10] dark:hover:bg-gray-50/[.15]",
//       )}
//     >
//       <div className="flex flex-row items-center gap-2">
//         <img className="rounded-full" width="32" height="32" alt="" src={img} />
//         <div className="flex flex-col">
//           <figcaption className="text-sm font-medium dark:text-white">
//             {name}
//           </figcaption>
//           <p className="text-xs font-medium dark:text-white/40">{username}</p>
//         </div>
//       </div>
//       <blockquote className="mt-2 text-sm">{body}</blockquote>
//     </figure>
//   );
// };