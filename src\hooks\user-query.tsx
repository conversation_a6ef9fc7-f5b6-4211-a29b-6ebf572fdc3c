import { useQuery } from '@tanstack/react-query'
import { getCurrentUser, getUserProjects } from '@/api/users-api.ts'
import { useNavigate } from '@tanstack/react-router'

export function useCurrentUser() {
  return useQuery({
    queryKey: ['user'],
    queryFn: () => getCurrentUser().then(res => res.data.user),
    staleTime: 1000 * 60 * 60, // 1小时内不重新请求
    refetchOnMount: true,
  })
}

export function useUserProjects() {
  const navigate = useNavigate()

  return useQuery({
    queryKey: ['user', 'projects'],
    queryFn: () => getUserProjects().then(res => {
      if (res.data.list.length === 0) {
        navigate({
          to: '/onboarding',
        })
      }
      return res.data.list
    }),
    staleTime: 1000 * 60 * 60, // 1小时内不重新请求
    refetchOnMount: true,
  })
}