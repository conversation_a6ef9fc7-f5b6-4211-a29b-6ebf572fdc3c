import React from 'react'
import { cn } from '@/lib/utils'

type LoadingProps = {
  text?: string
  className?: string
  isVisible?: boolean
}

export function Loading({
  text = '数据加载中，请等待',
  className,
  isVisible = true,
  ...props
}: LoadingProps & React.HTMLAttributes<HTMLDivElement>) {
  if (!isVisible) return null

  return (
    <div 
      className={cn(
        'fixed inset-0 z-50 flex flex-col items-center justify-center gap-4',
        'bg-white/80 backdrop-blur-sm',
        className
      )} 
      {...props}
    >
      <div 
        className="h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent" 
        aria-label="loading"
      />
      {text && (
        <p className="text-base font-medium text-gray-700">{text}</p>
      )}
    </div>
  )
} 