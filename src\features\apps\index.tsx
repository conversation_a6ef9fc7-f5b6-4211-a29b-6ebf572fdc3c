import { useCallback, useState } from 'react'
import {
  IconAdjustmentsHorizontal,
  IconSortAscendingLetters,
  IconSortDescendingLetters,
  IconBrandDocker,
  IconBrandGithub,
  IconBrandOpenai,
  IconCode,
  IconDatabase,
  IconBrandChrome,
  IconTools,
  IconJson,
  IconRobot,
  IconMessageChatbot,
  IconMail,
  IconLetterH,
  IconBrandGithubCopilot,
} from '@tabler/icons-react'
import { useNavigate } from '@tanstack/react-router'
import { useLanguage } from '@/context/language-context'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { LanguageSwitch } from '@/components/language-switch.tsx'
import { useApps } from '@/hooks/apps-query'
import { Loader2 } from 'lucide-react'
import { App } from './data/schema'
import SmartSelect from '@/features/apps/components/smart-select.tsx'
import { useSidebar } from '@/components/ui/sidebar.tsx'
import { addAppToDeploymentPlan } from '@/utils/deployment-plan-utils'
import { toast } from 'sonner'

export default function Apps() {
  const { t } = useLanguage()
  const navigate = useNavigate()
  const [sort, setSort] = useState('ascending')
  const [appType, setAppType] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [showSmartSelect, setShowSmartSelect] = useState(false)
  const { setOpen } = useSidebar()
  const [smartSelectList, setSmartSelectList] = useState<string[]>([])
  const memoizedFallback = useCallback((list:string[])=>{
    setSmartSelectList(list)
  },[setSmartSelectList])


  // Fetch apps data using the hook
  const { data: appsData, isLoading, isError } = useApps()

  // 不再需要更新应用连接状态的钩子
  // const updateAppConnection = useUpdateAppConnection()

  // Filter and sort apps
  const filteredApps = appsData
    ? [...appsData]
      .filter((app: App) => app && app.name) // 确保应用和应用名称存在
      .sort((a: App, b: App) =>
        sort === 'ascending'
          ? (a.name || '').localeCompare(b.name || '')
          : (b.name || '').localeCompare(a.name || ''),
      )
      .filter((app: App) =>
        appType === 'deployed'
          ? app.integrated
          : appType === 'notDeployed'
            ? !app.integrated
            : true,
      )
      .filter((app: App) => app.name && app.name.toLowerCase().includes(searchTerm.toLowerCase()))
      .filter((app: App) => smartSelectList.length > 0 ? app.name && smartSelectList.includes(app.name) : true)
    : []

  // 根据 ID 获取对应的图标
  const getIconById = (id: string) => {
    switch (id) {
      case '1':
        return <IconLetterH />
      case '2':
        return <IconCode />
      case '3':
        return <IconDatabase />
      case '4':
        return <IconBrandChrome />
      case '5':
        return <IconTools />
      case '6':
        return <IconJson />
      case '7':
        return <IconMessageChatbot />
      case '8':
        return <IconMail />
      case '9':
        return <IconRobot />
      case '10':
        return <IconBrandDocker />
      case '11':
        return <IconBrandGithub />
      case '12':
        return <IconBrandOpenai />
      case '13':
        return <IconDatabase />
      case '14':
        return <IconDatabase />
      case '15':
        return <IconDatabase />
      default:
        return null
    }
  }

  // 根据 ID 获取对应的图标颜色
  const getIconColor = (id: string) => {
    switch (id) {
      case '1':
        return 'text-blue-500'
      case '2':
        return 'text-indigo-500'
      case '3':
        return 'text-indigo-600'
      case '4':
        return 'text-gray-700'
      case '5':
        return 'text-green-600'
      case '6':
        return 'text-orange-500'
      case '7':
        return 'text-purple-500'
      case '8':
        return 'text-blue-600'
      case '9':
        return 'text-red-500'
      case '10':
        return 'text-blue-500'
      case '11':
        return 'text-gray-800'
      case '12':
        return 'text-green-500'
      default:
        return 'text-indigo-600'
    }
  }

  // 添加到部署方案后的对话框状态
  const [isNavigateDialogOpen, setIsNavigateDialogOpen] = useState(false)
  const [currentApp, setCurrentApp] = useState<App | null>(null)

  // 处理添加到部署方案
  const handleAddToDeploymentPlan = (app: App) => {
    // 添加应用到部署方案
    const success = addAppToDeploymentPlan(app)

    if (success) {
      // 显示成功消息
      toast.success(`${app.name} ${t.deploymentPlan.addedToDeploymentPlan}`)

      // 保存当前应用并打开导航对话框
      setCurrentApp(app)
      setIsNavigateDialogOpen(true)
    } else {
      // 显示错误消息
      toast.error(`${t.deploymentPlan.addToDeploymentPlanFailed}: ${app.name}`)
    }
  }

  // 处理导航到部署方案页面
  const handleNavigateToDeploymentPlan = () => {
    setIsNavigateDialogOpen(false)
    navigate({ to: '/deployment-plan' })
  }


  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <Search />
        <div className="ml-auto flex items-center gap-4">
          <LanguageSwitch />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Content ===== */}
      <Main fixed className="flex h-[calc(100vh-64px)] overflow-hidden">
        {/* ===== Left Content ===== */}
        <div
          className={`peer-[.header-fixed]/header:mt-16 px-4 py-6 transition-all duration-300 ${showSmartSelect ? 'w-2/3' : 'w-full'} flex flex-col`}>
          <div className="flex items-center justify-between flex-shrink-0">
            <div className="flex items-center gap-1">
              <h1 className="text-2xl font-bold tracking-tight">
                {t.apps.title}
              </h1>
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-2 ml-2 px-3 py-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                onClick={() => {
                  if (showSmartSelect) {
                    setOpen(true)
                  } else {
                    setOpen(false)
                  }
                  setShowSmartSelect((prev) => !prev)
                }}
              >
                <div style={{ transform: 'scale(1.3)', marginRight: '2px' }}>
                  <IconBrandGithubCopilot className="text-gray-500 dark:text-gray-400" size={36} />
                </div>
                <span className="text-sm font-medium">{t.apps.smartSelection}</span>
              </Button>
            </div>
          </div>
          <div className="my-4 flex items-end justify-between sm:my-0 sm:items-center flex-shrink-0">
            <div className="flex flex-col gap-4 sm:my-4 sm:flex-row">
              <Input
                placeholder={t.apps.filter}
                className="h-9 w-40 lg:w-[250px]"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Select value={appType} onValueChange={setAppType}>
                <SelectTrigger className="w-36">
                  <SelectValue>
                    {appType === 'all' ? t.apps.all :
                      appType === 'deployed' ? t.apps.deployed :
                        t.apps.notDeployed}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t.apps.all}</SelectItem>
                  <SelectItem value="deployed">{t.apps.deployed}</SelectItem>
                  <SelectItem value="notDeployed">{t.apps.notDeployed}</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                className="ml-auto h-8 w-20 rounded-md"
                onClick={
                  () => {
                    setSmartSelectList([])
                    setSearchTerm('')
                    setAppType('all')
                    setSort('ascending')
                  }
                }
              >重置</Button>
            </div>

            <Select value={sort} onValueChange={setSort}>
              <SelectTrigger className="w-16">
                <SelectValue>
                  <IconAdjustmentsHorizontal size={18} />
                </SelectValue>
              </SelectTrigger>
              <SelectContent align="end">
                <SelectItem value="ascending">
                  <div className="flex items-center gap-4">
                    <IconSortAscendingLetters size={16} />
                    <span>{t.apps.ascending}</span>
                  </div>
                </SelectItem>
                <SelectItem value="descending">
                  <div className="flex items-center gap-4">
                    <IconSortDescendingLetters size={16} />
                    <span>{t.apps.descending}</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Separator className="shadow-sm flex-shrink-0" />

          {isLoading ? (
            <div className="flex justify-center items-center py-20 flex-grow">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              {t.apps.loading}
            </div>
          ) : isError ? (
            <div className="flex justify-center items-center py-20 flex-grow">
              <p className="text-destructive">{t.apps.error}</p>
            </div>
          ) : (
            <div className="flex-grow overflow-y-auto">
              <ul
                className="grid grid-flow-row auto-rows-min gap-4 pt-4 pb-4 pr-4 custom-scrollbar sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4">
                {filteredApps.map((app: App) => app && app.id ? (
                  <li
                    key={app.id}
                    className="rounded-lg border p-5 hover:shadow-md flex flex-col h-[200px]"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-4">
                        <div
                          className="flex size-14 items-center justify-center rounded-lg p-2 bg-gray-50 dark:bg-gray-800"
                        >
                          {/* 使用 getIconById 函数渲染图标 */}
                          <div className={`text-4xl ${getIconColor((app.logo || '') as string)}`}>
                            {getIconById((app.logo || '') as string)}
                          </div>
                        </div>
                        <div>
                          <h2 className="font-semibold">{app.name || ''}</h2>
                        </div>
                      </div>
                    </div>
                    <p className="line-clamp-2 text-gray-500 text-sm flex-grow">{app.desc || ''}</p>
                    <div className="flex justify-between items-center mt-auto pt-4">
                      {app.tag ? (
                        <div className="text-xs px-3 py-1.5 bg-gray-100 dark:bg-gray-800 rounded-md font-medium">
                          {app.tag === '工具' ? t.apps.tool :
                            app.tag === '博客' ? t.apps.blog :
                              app.tag === 'AI' ? t.apps.ai : (app.tag || '')}
                        </div>
                      ) : (
                        <div></div>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        className="ml-auto rounded-md bg-transparent"
                        onClick={() => handleAddToDeploymentPlan(app)}
                      >
                        {t.deploymentPlan.integrate}
                      </Button>
                    </div>
                  </li>
                ) : null)}
              </ul>
            </div>
          )}
        </div>
        {/* ===== Right Content ===== */}
        {showSmartSelect && (
          <div className="w-1/3 peer-[.header-fixed]/header:mt-16 px-4 py-4 flex flex-col">
            <SmartSelect
              fallback={memoizedFallback}
              close={() => {
                setShowSmartSelect(false)
                setOpen(true)
              }}
            />
          </div>
        )}
      </Main>

      {/* 导航到部署方案的对话框 */}
      <Dialog open={isNavigateDialogOpen} onOpenChange={setIsNavigateDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{t.deploymentPlan.addToDeploymentPlan}</DialogTitle>
            <DialogDescription>
              {currentApp?.name} {t.deploymentPlan.addedToDeploymentPlan}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex justify-between sm:justify-between">
            <Button variant="outline" onClick={() => setIsNavigateDialogOpen(false)}>
              {t.deploymentPlan.cancel}
            </Button>
            <Button onClick={handleNavigateToDeploymentPlan} className="ml-2">
              {t.deploymentPlan.viewDeploymentPlan}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

















