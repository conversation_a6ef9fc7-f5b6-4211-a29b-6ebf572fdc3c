import { ReactNode } from 'react'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { PanelRightIcon } from 'lucide-react'
import { Separator } from '@/components/ui/separator'

interface PanelLayoutProps {
  title: string;
  onClose: () => void;
  children: ReactNode;
  footer?: ReactNode;
  position?: 'left' | 'right';
}

export default function PanelLayout({ title, onClose, children, footer, position = 'right' }: PanelLayoutProps) {
  const isLeftPanel = position === 'left';

  return (
    <div className={`fixed ${isLeftPanel ? 'left-4' : 'right-4'} top-[80px] bottom-4 z-10 w-[450px] max-w-[40vw] lg:max-w-[50vw] transition-all duration-200 ease-out h-[calc(100vh-96px)] max-h-[calc(100vh-96px)] overflow-hidden`}>
      <Card className="h-full flex flex-col rounded-lg shadow-md">
        <CardHeader className="border-b border-b-gray-200 dark:border-b-gray-700 px-3 py-3 h-16 flex flex-row items-center justify-between">
          <div className="flex items-center">
            <Button
              data-sidebar="trigger"
              data-slot="sidebar-trigger"
              variant="outline"
              size="icon"
              className={cn('size-7', 'scale-125 sm:scale-100', 'mr-3')}
              onClick={onClose}
            >
              <PanelRightIcon />
              <span className="sr-only">Toggle Sidebar</span>
            </Button>
            <Separator orientation="vertical" className="h-4 mr-3" />
            <h3 className="text-lg font-medium">{title}</h3>
          </div>
          <div className="flex items-center">
            {/* Right side area for additional controls if needed */}
          </div>
        </CardHeader>

        <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
          {children}
        </CardContent>

        {footer && (
          <div className="px-3 pt-2 border-t flex items-center justify-center h-16">
            {footer}
          </div>
        )}
      </Card>
    </div>
  )
}
