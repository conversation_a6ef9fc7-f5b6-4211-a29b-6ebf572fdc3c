import { SVGProps } from 'react'

export type IconSvgProps = SVGProps<SVGSVGElement> & {
  size?: number;
};

export const Logo: React.FC<IconSvgProps> = ({
                                               size = 36,
                                               width,
                                               height,
                                               ...props
                                             }) => (
  // <svg
  //   fill="none"
  //   height={size || height}
  //   width={size || width}
  //   {...props}
  //   viewBox="0 0 24 24"
  //   stroke="currentColor"
  //   strokeWidth="2"
  //   strokeLinecap="round"
  //   strokeLinejoin="round"
  //   className="mr-2 h-6 w-6"
  // >
  //   <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path>
  // </svg>
<svg
  fill="none"
  height={size || height}
  width={size || width}
  xmlns="http://www.w3.org/2000/svg"
  viewBox="0 0 20 20"
  className="mr-2 align-middle"
  {...props}
>
  <path
    d="M7.98747 4.17997C7.71458 4.84269 7.48643 5.62692 7.31842 6.5H12.6816C12.5136 5.62692 12.2854 4.84269 12.0125 4.17997C11.7106 3.44666 11.3637 2.88614 11.0041 2.51675C10.646 2.14894 10.3069 2 10 2C9.69311 2 9.35398 2.14894 8.99591 2.51675C8.6363 2.88614 8.28942 3.44666 7.98747 4.17997ZM7.89053 2.28104C7.57693 2.70725 7.30036 3.22224 7.06279 3.79922C6.74205 4.57814 6.48315 5.49258 6.3012 6.5H2.80423C3.80285 4.45074 5.65108 2.89163 7.89053 2.28104ZM12.1095 2.28105C12.4231 2.70725 12.6996 3.22225 12.9372 3.79922C13.2579 4.57814 13.5169 5.49258 13.6988 6.5H17.1958C16.1972 4.45074 14.3489 2.89163 12.1095 2.28105ZM17.6016 7.5H13.85C13.8883 7.81085 13.9196 8.12848 13.9435 8.45184C14.5741 7.90505 15.5558 7.9342 16.1491 8.53911C16.5732 8.9716 17.2324 9.52202 17.9977 9.80794C17.9788 9.00364 17.8411 8.22871 17.6016 7.5ZM12.8421 7.5C12.9158 8.06631 12.9653 8.66086 12.9872 9.27635C12.5172 9.60676 11.9575 9.8902 11.3592 9.97446C10.679 10.0703 9.9999 10.6426 10 11.5002L10.0001 12.5H7.15793C7.0557 11.7145 7 10.8747 7 10C7 9.1253 7.0557 8.28549 7.15793 7.5H12.8421ZM7.31842 13.5H10.0002L10.0003 14.0001C10.0003 15.4242 10.4537 16.5467 11.0741 17.4087C11.0509 17.4344 11.0275 17.4592 11.0041 17.4832C10.646 17.8511 10.3069 18 10 18C9.69311 18 9.35398 17.8511 8.99591 17.4832C8.6363 17.1139 8.28942 16.5533 7.98747 15.82C7.71458 15.1573 7.48643 14.3731 7.31842 13.5ZM7.89053 17.719C5.65107 17.1084 3.80285 15.5493 2.80423 13.5H6.3012C6.48315 14.5074 6.74205 15.4219 7.06279 16.2008C7.30036 16.7778 7.57693 17.2928 7.89053 17.719ZM2.39838 12.5H6.14996C6.0521 11.7051 6 10.8658 6 10C6 9.13416 6.0521 8.2949 6.14996 7.5H2.39838C2.13985 8.28655 2 9.12694 2 10C2 10.8731 2.13985 11.7135 2.39838 12.5ZM18.5013 10.9647C17.1302 10.7717 16.0154 9.83093 15.4352 9.23931C15.204 9.00357 14.796 9.00354 14.5648 9.23926C13.9845 9.83085 12.8695 10.7716 11.4987 10.9647C11.2253 11.0032 11 11.2239 11.0001 11.5001L11.0004 14C11.0004 17.4685 14.1823 18.7273 14.8702 18.9589C14.9563 18.9879 15.0443 18.9879 15.1304 18.9589C15.8183 18.7273 19 17.4685 19 14V11.5C19 11.2239 18.7748 11.0032 18.5013 10.9647Z"
    fill="#212121" />
</svg>
);
