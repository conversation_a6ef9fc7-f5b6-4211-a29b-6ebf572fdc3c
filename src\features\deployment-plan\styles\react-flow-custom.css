/* 自定义 ReactFlow 样式 */

/* 基本光标样式 */
body {
  cursor: default;
}

/* 拖动模式下的光标样式 */
.pan-mode {
  cursor: grab;
}

.pan-mode:active {
  cursor: grabbing;
}

/* 选择模式下的光标样式 */
.select-mode {
  cursor: default;
}

/* 确保画布面板继承父元素的光标样式 */
.react-flow__pane {
  cursor: inherit;
}

/* 当按下 Shift 键时，显示十字光标 */
.react-flow__pane.selection {
  cursor: crosshair !important;
}

/* 当悬停在节点上时，显示移动光标并添加柔和的悬停效果 */
.react-flow__node {
  transition: transform 0.2s ease, box-shadow 0.2s ease, filter 0.2s ease;
}

.react-flow__node:hover {
  cursor: move !important;
  filter: brightness(1.02);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

/* 当悬停在连接点上时，显示指针光标 */
.react-flow__handle:hover {
  cursor: pointer !important;
}

/* 当悬停在边上时，显示指针光标 */
.react-flow__edge:hover {
  cursor: pointer !important;
}

/* 当拖动连接点时，显示十字光标 */
.react-flow.dragging {
  cursor: crosshair !important;
}

/* 自定义控制面板样式 */
.react-flow__controls {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.react-flow__controls-button {
  border: none !important;
  border-bottom: 1px solid var(--border) !important;
}

.react-flow__controls-button:last-child {
  border-bottom: none !important;
}

/* 自定义背景样式 */
.react-flow__background {
  opacity: 0.5;
}

/* 自定义节点选中样式 */
.react-flow__node.selected {
  box-shadow: 0 0 0 2px color-mix(in srgb, var(--primary) 30%, transparent) !important;
  border-radius: 8px !important;
  transition: box-shadow 0.2s ease-in-out;
}

/* 自定义边选中样式 */
.react-flow__edge.selected .react-flow__edge-path {
  stroke-width: 2.5 !important;
  stroke: color-mix(in srgb, var(--primary) 70%, transparent) !important;
  filter: drop-shadow(0 0 2px color-mix(in srgb, var(--primary) 20%, transparent));
}

/* 基本边样式 - 不强制移除箭头，允许自定义 */
.react-flow__edge-path {
  stroke-width: 2px; /* 设置基本线宽 */
}

/* 连接线基本样式 */
.react-flow__connection-path {
  stroke-width: 2.5 !important;
  stroke: black !important;
  opacity: 0.8;
}

/* 增强连接线的可见性 */
.react-flow__connection {
  z-index: 1000 !important;
}

/* 增强连接点的可见性和交互体验 */
.react-flow__handle {
  width: 10px !important;
  height: 10px !important;
  background-color: var(--primary) !important;
  border: 2px solid white !important;
  transition: transform 0.2s ease, box-shadow 0.2s ease !important;
}

.react-flow__handle:hover {
  transform: scale(1.5) !important;
  box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.2) !important;
}

/* 确保连接拖动时的状态优先级高于其他状态 */
.react-flow.dragging .react-flow__node:hover {
  cursor: crosshair !important;
  transform: none !important;
  filter: none !important;
  box-shadow: none !important;
}
