import { useState, use<PERSON><PERSON>back, useRef, useEffect } from 'react'
import {
  ReactFlow,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  NodeTypes,
  Panel,
} from '@xyflow/react'
import '@xyflow/react/dist/style.css'
import './styles/react-flow-custom.css'
import './styles/cursor-fix.css'
import './styles/toolbar-effects.css'
import './styles/connection-line.css'
import './styles/handle-visibility.css'
import './styles/connect-mode.css'
import './styles/edge-styles.css'
import { useLanguage } from '@/context/language-context'
import { Main } from '@/components/layout/main'
import { Header } from '@/components/layout/header'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { LanguageSwitch } from '@/components/language-switch'
import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogContent, <PERSON>alogD<PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from '@/components/ui/dialog'

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2 } from 'lucide-react'
import { IconDeviceFloppy, IconCode, IconInfoCircle, IconLayersSubtract, IconPointer, IconHandGrab, IconApps, IconLine, IconCircleDottedLetterA, IconServer, IconLayoutSidebar } from '@tabler/icons-react'
import { getIconById, getIconColor } from '@/features/apps/utils/icon-utils'
import { App } from '@/features/apps/data/schema'
import CustomNode from './components/custom-node'
import NodeGroup from './components/node-group'
import AIArchitect from './components/ai-architect'
import DeploymentConfigPanel from './components/deployment-config-panel'
import NodeEditPanel from './components/node-edit-panel'

import CreateHostDialog from './components/create-host-dialog'
import { useDeploymentPlan, useDeploymentPlanApps, useSaveDeploymentPlan } from '@/hooks/deployment-plan-query'
import { useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'

// 定义节点类型
const nodeTypes: NodeTypes = {
  custom: CustomNode,
  group: NodeGroup,
};

// 初始节点数据
const initialNodes: Node[] = [
  {
    id: 'node-1',
    type: 'custom',
    position: { x: 100, y: 100 },
    data: {
      label: 'Headscale',
      description: '一个自托管的开源 Tailscale 控制服务器实现。',
      icon: '2',
      tag: '工具',
    },
  },
  {
    id: 'node-2',
    type: 'custom',
    position: { x: 400, y: 100 },
    data: {
      label: 'PostgreSQL',
      description: '强大的开源关系型数据库系统。',
      icon: '3',
      tag: '数据库',
    },
  },
  {
    id: 'node-3',
    type: 'custom',
    position: { x: 250, y: 300 },
    data: {
      label: 'Halo',
      description: '强大易用的开源建站工具。',
      icon: '1',
      tag: '博客',
    },
  },
]

// 初始边数据
const initialEdges: Edge[] = [
  {
    id: 'edge-1-2',
    source: 'node-1',
    target: 'node-2',
    animated: true,
    type: 'smoothstep',
    style: { strokeWidth: 2 },
  },
  {
    id: 'edge-2-3',
    source: 'node-2',
    target: 'node-3',
    animated: true,
    type: 'smoothstep',
    style: { strokeWidth: 2 },
  },
]

export default function DeploymentPlan() {
  const { t } = useLanguage()
  const queryClient = useQueryClient()
  // 初始化时使用空数组，稍后会从 localStorage 或初始数据加载
  const [nodes, setNodes, onNodesChange] = useNodesState([] as unknown as Node[])
  const [edges, setEdges, onEdgesChange] = useEdgesState([] as unknown as Edge[])
  const [isAddAppDialogOpen, setIsAddAppDialogOpen] = useState(false)
  const [isEditNodeDialogOpen, setIsEditNodeDialogOpen] = useState(false)
  const [isNodeEditPanelOpen, setIsNodeEditPanelOpen] = useState(false)
  const [isCreateNodeDialogOpen, setIsCreateNodeDialogOpen] = useState(false)
  const [selectedNode, setSelectedNode] = useState<Node | null>(null)
  const [nodeName, setNodeName] = useState('')
  const [nodeDescription, setNodeDescription] = useState('')
  const [nodeTag, setNodeTag] = useState('自定义')
  const [nodeIcon, setNodeIcon] = useState('2')
  const [clickPosition, setClickPosition] = useState({ x: 0, y: 0 })
  const [isCreateGroupDialogOpen, setIsCreateGroupDialogOpen] = useState(false)
  const [groupName, setGroupName] = useState('新分组')
  const [selectedNodes, setSelectedNodes] = useState<string[]>([])
  const [interactionMode, setInteractionMode] = useState<'pan' | 'select'>('pan')
  const [toolMode, setToolMode] = useState<'none' | 'connect'>('none')
  const [connectionStart, setConnectionStart] = useState<string | null>(null)
  const [lineStyle, setLineStyle] = useState<'dashed' | 'dashed-arrow-open' | 'solid-arrow-open' | 'dashed-arrow-filled' | 'solid-arrow-filled'>('dashed')
  const [isLineStylePanelOpen, setIsLineStylePanelOpen] = useState(false)
  const [isAIArchitectOpen, setIsAIArchitectOpen] = useState(true)

  const [isCreateHostDialogOpen, setIsCreateHostDialogOpen] = useState(false)
  const [isHostNodeEditPanelOpen, setIsHostNodeEditPanelOpen] = useState(false)
  const [isLeftPanelOpen, setIsLeftPanelOpen] = useState(true)
  const [selectedHostNode, setSelectedHostNode] = useState<Node | null>(null)
  const reactFlowWrapper = useRef<HTMLDivElement>(null)
  const reactFlowInstance = useRef<any>(null)
  const { data: planData, isLoading: isPlanLoading } = useDeploymentPlan()
  const { data: appsData, isLoading: isAppsLoading, isError } = useDeploymentPlanApps()
  const { mutate: savePlan, isPending: isSaving } = useSaveDeploymentPlan()

  const isLoading = isPlanLoading || isAppsLoading

  // 当聊天框或节点编辑面板展开/关闭状态变化时，重新计算视图
  useEffect(() => {
    // 使用两次延迟，第一次较短以快速响应，第二次较长以确保完全过渡后再次调整
    const timer1 = setTimeout(() => {
      reactFlowInstance.current?.fitView({ padding: 0.2, duration: 200 })
    }, 50)

    const timer2 = setTimeout(() => {
      reactFlowInstance.current?.fitView({ padding: 0.5, duration: 300 })
    }, 250)

    return () => {
      clearTimeout(timer1)
      clearTimeout(timer2)
    }
  }, [isAIArchitectOpen, isNodeEditPanelOpen, isHostNodeEditPanelOpen])

  // 加载部署方案数据 - 优先使用 localStorage 数据，其次是 API 数据，最后是初始数据
  useEffect(() => {
    try {
      // 首先尝试从 localStorage 加载数据
      const savedData = localStorage.getItem('deployment-plan-data')
      if (savedData) {
        const parsedData = JSON.parse(savedData)
        if (parsedData.nodes && parsedData.nodes.length > 0) {
          setNodes(parsedData.nodes)
          setEdges(parsedData.edges || [])
          return // 如果从 localStorage 加载了数据，就不再使用其他数据源
        }
      }

      // 如果 localStorage 没有数据，尝试使用 API 数据
      if (planData && planData.nodes && planData.nodes.length > 0) {
        setNodes(planData.nodes)
        setEdges(planData.edges || [])
        return // 如果从 API 加载了数据，就不再使用初始数据
      }

      // 如果两个数据源都没有数据，则使用初始数据
      setNodes(initialNodes)
      setEdges(initialEdges)
    } catch (error) {
      console.error('Failed to load deployment plan data:', error)
      // 出错时使用初始数据
      setNodes(initialNodes)
      setEdges(initialEdges)
    }
  }, [planData, setNodes, setEdges])



  // 获取当前连接线样式的属性
  const getConnectionLineProperties = useCallback(() => {
    let strokeDasharray;
    let animated = true;
    let markerEnd;

    switch (lineStyle) {
      case 'dashed':
        strokeDasharray = '5,3';
        animated = true;
        markerEnd = undefined;
        break;
      case 'dashed-arrow-open':
        strokeDasharray = '5,3';
        animated = true;
        markerEnd = 'url(#arrow-open)';
        break;
      case 'solid-arrow-open':
        strokeDasharray = undefined;
        animated = false;
        markerEnd = 'url(#arrow-open)';
        break;
      case 'dashed-arrow-filled':
        strokeDasharray = '5,3';
        animated = true;
        markerEnd = 'url(#arrow-filled)';
        break;
      case 'solid-arrow-filled':
        strokeDasharray = undefined;
        animated = false;
        markerEnd = 'url(#arrow-filled)';
        break;
      default:
        strokeDasharray = '5,3';
        animated = true;
        markerEnd = undefined;
    }

    return { strokeDasharray, animated, markerEnd };
  }, [lineStyle]);

  // 处理连线模式下的节点样式
  useEffect(() => {
    if (connectionStart) {
      // 为起点节点添加特殊样式
      setNodes((nds) =>
        nds.map((node) => {
          if (node.id === connectionStart) {
            // 添加 connection-source 类
            node.className = (node.className || '') + ' connection-source';
          } else {
            // 移除 connection-source 类
            if (node.className) {
              node.className = node.className.replace(' connection-source', '');
            }
          }
          return node;
        })
      );
    } else {
      // 移除所有节点的 connection-source 类
      setNodes((nds) =>
        nds.map((node) => {
          if (node.className) {
            node.className = node.className.replace(' connection-source', '');
          }
          return node;
        })
      );
    }
  }, [connectionStart, setNodes])

  // 当节点或边发生变化时自动保存到 localStorage
  useEffect(() => {
    if (nodes.length > 0 || edges.length > 0) {
      try {
        localStorage.setItem('deployment-plan-data', JSON.stringify({ nodes, edges }))
      } catch (error) {
        console.error('Failed to auto save deployment plan:', error)
      }
    }
  }, [nodes, edges])

  // 处理连接 - 平滑曲线，无箭头，无验证
  const onConnect = useCallback(
    (params: Connection) => {
      // 检查是否已存在相同的连接
      const connectionExists = edges.some(
        edge =>
          (edge.source === params.source && edge.target === params.target) ||
          (edge.source === params.target && edge.source === params.source)
      );

      if (connectionExists) {
        toast.error(t.deploymentPlan.connectionExists);
        return;
      }

      // 不再需要根据连接点ID设置类名，所有连接线都使用黑色

      // 使用 getConnectionLineProperties 函数获取连接线的样式
      const { markerEnd, strokeDasharray, animated } = getConnectionLineProperties();

      // 创建边对象
      const edge: Edge = {
        ...params,
        id: `edge-${params.source}-${params.target}-${Date.now()}`,
        animated,
        type: 'smoothstep',
        style: {
          strokeWidth: 2,
          strokeDasharray
        },
        markerEnd,
        markerStart: undefined,
        className: lineStyle, // 添加类名，以便CSS选择器可以根据类名应用不同的样式
        data: {
          lineStyle,
          type: strokeDasharray ? 'dashed' : 'solid',
          animated: animated ? 'true' : 'false',
          hasArrow: markerEnd ? 'true' : 'false',
          arrowType: markerEnd?.includes('arrow-open') ? 'open' : markerEnd?.includes('arrow-filled') ? 'filled' : 'none'
        } // 存储连线样式，以便后续可以编辑和CSS选择
      };

      // 直接添加连接，不进行验证
      setEdges((eds) => addEdge(edge, eds));
      toast.success(t.deploymentPlan.connectionSuccess);
    },
    [edges, setEdges, lineStyle, t.deploymentPlan.connectionExists, t.deploymentPlan.connectionSuccess]
  )

  // 处理节点点击 - 选中节点并更新选中节点列表
  const onNodeClick = useCallback(
    (event: React.MouseEvent, node: Node) => {
      // 如果处于连线模式
      if (toolMode === 'connect') {
        if (!connectionStart) {
          // 设置连线起点
          setConnectionStart(node.id);
          toast.info(`已选择起点: ${node.data.label}`);
        } else if (connectionStart !== node.id) {
          // 创建从起点到当前点击节点的连接
          const params: Connection = {
            source: connectionStart,
            target: node.id,
            sourceHandle: null,
            targetHandle: null,
          };

          // 使用 onConnect 处理连接
          onConnect(params);

          // 重置连线状态，继续保持连线模式
          setConnectionStart(null);
        }
        return;
      }

      // 选中节点
      setSelectedNode(node)

      // 如果按住 Alt 键，并且已经有一个节点被选中，则创建连接
      if (event.altKey && selectedNode && selectedNode.id !== node.id) {
        // 创建从已选中节点到当前点击节点的连接
        const params: Connection = {
          source: selectedNode.id,
          target: node.id,
          sourceHandle: null,
          targetHandle: null,
        };

        // 使用 onConnect 处理连接
        onConnect(params);

        // 更新选中的节点列表，包含两个连接的节点
        setSelectedNodes([selectedNode.id, node.id]);

        // 更新节点的选中状态
        setNodes(nds =>
          nds.map(n => ({
            ...n,
            selected: n.id === selectedNode.id || n.id === node.id
          }))
        );

        return;
      }

      // 如果按住 Ctrl 键，则添加或移除节点到选中列表
      if (event.ctrlKey) {
        setSelectedNodes(prev => {
          if (prev.includes(node.id)) {
            return prev.filter(id => id !== node.id);
          } else {
            return [...prev, node.id];
          }
        });
      } else {
        // 如果没有按住 Ctrl 键，则只选中当前节点
        setSelectedNodes([node.id]);
      }

      // 更新节点的选中状态
      setNodes(nds =>
        nds.map(n => {
          // 如果按住 Ctrl 键，则保留之前选中的节点状态
          if (event.ctrlKey) {
            if (n.id === node.id) {
              // 切换当前点击节点的选中状态
              return {
                ...n,
                selected: !selectedNodes.includes(n.id)
              };
            } else {
              // 保持其他节点的选中状态
              return {
                ...n,
                selected: selectedNodes.includes(n.id)
              };
            }
          } else {
            // 如果没有按住 Ctrl 键，则只选中当前节点
            return {
              ...n,
              selected: n.id === node.id
            };
          }
        })
      );

      // 新增：如果已经有编辑面板打开，则自动切换到当前点击节点的编辑面板
      if (isNodeEditPanelOpen || isHostNodeEditPanelOpen) {
        // 检查节点类型，如果是主机节点，则打开主机节点编辑面板
        if (node.data.nodeType === 'host') {
          setSelectedHostNode(node)



          // 确保AI架构师聊天框关闭
          if (isAIArchitectOpen) {
            setIsAIArchitectOpen(false)
          }

          // 确保普通节点编辑面板关闭
          if (isNodeEditPanelOpen) {
            setIsNodeEditPanelOpen(false)
          }

          // 延迟一点打开主机节点编辑面板，确保过渡效果平滑
          setTimeout(() => {
            setIsHostNodeEditPanelOpen(true)
          }, 50)
        } else {
          // 普通节点处理逻辑
          setSelectedNode(node)
          // 使用类型断言确保 data 符合预期的类型
          const nodeData = node.data as unknown as { label: string; description?: string };
          setNodeName(nodeData.label)
          setNodeDescription(nodeData.description || '')



          // 确保AI架构师聊天框关闭
          if (isAIArchitectOpen) {
            setIsAIArchitectOpen(false)
          }

          // 确保主机节点编辑面板关闭
          if (isHostNodeEditPanelOpen) {
            setIsHostNodeEditPanelOpen(false)
          }

          // 延迟一点打开节点编辑面板，确保过渡效果平滑
          setTimeout(() => {
            setIsNodeEditPanelOpen(true)
          }, 50)
        }
      }
    },
    [selectedNode, onConnect, toolMode, connectionStart, selectedNodes, setNodes, isNodeEditPanelOpen, isHostNodeEditPanelOpen, isAIArchitectOpen]
  )

  // 处理节点双击 - 打开编辑面板
  const onNodeDoubleClick = useCallback(
    (_: React.MouseEvent, node: Node) => {
      // 检查节点类型，如果是主机节点，则打开主机节点编辑面板
      if (node.data.nodeType === 'host') {
        setSelectedHostNode(node)



        // 确保AI架构师聊天框关闭，主机节点编辑面板打开
        if (isAIArchitectOpen) {
          setIsAIArchitectOpen(false)
        }

        // 确保普通节点编辑面板关闭
        if (isNodeEditPanelOpen) {
          setIsNodeEditPanelOpen(false)
        }

        // 延迟一点打开主机节点编辑面板，确保过渡效果平滑
        setTimeout(() => {
          setIsHostNodeEditPanelOpen(true)
        }, 50)
      } else {
        // 普通节点处理逻辑
        setSelectedNode(node)
        // 使用类型断言确保 data 符合预期的类型
        const nodeData = node.data as unknown as { label: string; description?: string };
        setNodeName(nodeData.label)
        setNodeDescription(nodeData.description || '')



        // 确保AI架构师聊天框关闭，节点编辑面板打开
        if (isAIArchitectOpen) {
          setIsAIArchitectOpen(false)
        }

        // 确保主机节点编辑面板关闭
        if (isHostNodeEditPanelOpen) {
          setIsHostNodeEditPanelOpen(false)
        }

        // 延迟一点打开节点编辑面板，确保过渡效果平滑
        setTimeout(() => {
          setIsNodeEditPanelOpen(true)
        }, 50)
      }
    },
    [isAIArchitectOpen, isNodeEditPanelOpen, isHostNodeEditPanelOpen]
  )

  // 处理画布点击
  const onPaneClick = useCallback(
    (event: React.MouseEvent) => {
      // 获取画布中的点击位置
      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect()
      if (!reactFlowBounds) return

      // 如果有 reactFlowInstance，则转换为画布坐标
      if (reactFlowInstance.current) {
        // 将屏幕坐标转换为流程图坐标
        const position = reactFlowInstance.current.screenToFlowPosition({
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top
        });

        // 保存点击位置
        setClickPosition(position);
      } else {
        // 如果没有 reactFlowInstance，则使用屏幕坐标
        const position = {
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top
        };

        // 保存点击位置
        setClickPosition(position);
      }

      // 如果按住了 Shift 键，则打开创建节点对话框
      if (event.shiftKey) {
        // 设置默认值
        setNodeName(`新节点 ${nodes.length + 1}`)
        setNodeDescription('自定义节点描述')
        setNodeTag('自定义')
        setNodeIcon('2')

        // 打开创建节点对话框
        setIsCreateNodeDialogOpen(true)
      }

      // 关闭连线样式面板
      setIsLineStylePanelOpen(false)

      // 取消选中当前选中的节点
      setSelectedNode(null)
      setSelectedNodes([])

      // 清除所有节点的选中状态
      setNodes(nds =>
        nds.map(n => ({
          ...n,
          selected: false
        }))
      );
    },
    [nodes.length, setIsLineStylePanelOpen, setNodes]
  )

  // 创建新节点
  const createNewNode = useCallback(() => {
    // 创建新节点
    const newNode: Node = {
      id: `node-${Date.now()}`,
      type: 'custom',
      position: clickPosition,
      data: {
        label: nodeName,
        description: nodeDescription,
        icon: nodeIcon,
        tag: nodeTag,
      },
    }

    setNodes((nds) => nds.concat(newNode))
    toast.success(t.deploymentPlan.nodeCreated)
    setIsCreateNodeDialogOpen(false)
  }, [clickPosition, nodeName, nodeDescription, nodeIcon, nodeTag, setNodes, t.deploymentPlan.nodeCreated])

  // 添加应用到画布
  const addAppToCanvas = useCallback(
    (app: App) => {
      // 验证应用数据是否完整
      if (!app || !app.id || !app.name) {
        toast.error('应用数据无效，请刷新页面后重试')
        return
      }

      try {
        // 计算新节点的位置 - 使用画布中心位置
        let position = { x: 200, y: 200 }; // 默认位置

        // 尝试获取画布中心位置
        if (reactFlowInstance.current) {
          const { x, y, zoom } = reactFlowInstance.current.getViewport();
          const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();

          if (reactFlowBounds) {
            // 计算画布中心点
            const centerX = (reactFlowBounds.width / 2 - x) / zoom;
            const centerY = (reactFlowBounds.height / 2 - y) / zoom;

            // 添加一点随机偏移，避免节点完全重叠
            position = {
              x: centerX + (Math.random() * 100 - 50),
              y: centerY + (Math.random() * 100 - 50)
            };
          }
        }

        // 确保 logo 是字符串
        const iconId = typeof app.logo === 'string' ? app.logo : app.id

        const newNode: Node = {
          id: `app-${app.id}-${Date.now()}`,
          type: 'custom',
          position: position,
          data: {
            label: app.name || `应用 ${app.id}`,
            description: app.desc || '',
            icon: iconId,
            tag: app.tag || '应用',
          },
        }

        // 使用函数式更新确保获取最新的状态
        setNodes((currentNodes) => {
          const updatedNodes = [...currentNodes, newNode]

          // 立即保存到 localStorage 以确保数据持久化
          try {
            localStorage.setItem('deployment-plan-data', JSON.stringify({
              nodes: updatedNodes,
              edges: edges
            }))
          } catch (error) {
            // Error handling for localStorage
          }

          return updatedNodes
        })

        toast.success(`${t.deploymentPlan.appAdded}: ${app.name}`)
        setIsAddAppDialogOpen(false)
      } catch (error) {
        toast.error('添加应用失败，请重试')
      }
    },
    [nodes, edges, setNodes, t.deploymentPlan.appAdded]
  )

  // 保存当前部署方案
  const handleSavePlan = useCallback(() => {
    try {
      // 保存到 localStorage
      localStorage.setItem('deployment-plan-data', JSON.stringify({ nodes, edges }))

      // 显示成功消息
      toast.success(t.deploymentPlan.planSaved)

      // 尝试保存到后端，但不影响用户体验
      try {
        savePlan(
          {
            id: 'default',
            data: { nodes, edges } as any
          },
          {
            onSuccess: () => {
              // Successfully saved to backend
            },
            onError: () => {
              // Backend API might not be available, but we don't show errors to the user
            }
          }
        )
      } catch (backendError) {
        // 忽略后端保存错误，不影响用户体验
      }
    } catch (error) {
      toast.error(t.deploymentPlan.planSaveFailed)
    }
  }, [nodes, edges, savePlan, t.deploymentPlan.planSaved, t.deploymentPlan.planSaveFailed])

  // 保存节点编辑 (对话框版本)
  const saveNodeEdit = useCallback((nodeNameParam?: string, nodeDescriptionParam?: string) => {
    if (selectedNode) {
      // 如果传递了参数，使用参数值；否则使用状态值（用于对话框）
      const finalNodeName = nodeNameParam !== undefined ? nodeNameParam : nodeName;
      const finalNodeDescription = nodeDescriptionParam !== undefined ? nodeDescriptionParam : nodeDescription;

      setNodes((nds) =>
        nds.map((node) => {
          if (node.id === selectedNode.id) {
            return {
              ...node,
              data: {
                ...node.data,
                label: finalNodeName,
                description: finalNodeDescription,
              },
            }
          }
          return node
        })
      )

      // 只有在没有传递参数时才关闭对话框（说明是从对话框调用的）
      if (nodeNameParam === undefined && nodeDescriptionParam === undefined) {
        setIsEditNodeDialogOpen(false)
      } else {
        // 从面板调用时显示成功提示
        toast.success('节点已保存')
      }
    }
  }, [selectedNode, nodeName, nodeDescription, setNodes])
  // 创建主机节点
  const createHostNode = useCallback((hostData: {
    name: string;
    description: string;
    ipAddress: string;
    operatingSystem: string;
    diskSpace: string;
    memory: string;
    cpuCores: string;
  }) => {
    try {
      // 计算新节点的位置 - 使用画布中心位置
      let position = { x: 200, y: 200 }; // 默认位置

      // 尝试获取画布中心位置
      if (reactFlowInstance.current) {
        const { x, y, zoom } = reactFlowInstance.current.getViewport();
        const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();

        if (reactFlowBounds) {
          // 计算画布中心点
          const centerX = (reactFlowBounds.width / 2 - x) / zoom;
          const centerY = (reactFlowBounds.height / 2 - y) / zoom;

          // 添加一点随机偏移，避免节点完全重叠
          position = {
            x: centerX + (Math.random() * 100 - 50),
            y: centerY + (Math.random() * 100 - 50)
          };
        }
      }

      // 创建新的主机节点
      const newNode: Node = {
        id: `host-${Date.now()}`,
        type: 'custom',
        position: position,
        data: {
          label: hostData.ipAddress, // 使用IP地址作为节点名称
          description: hostData.name, // 使用主机名称作为描述
          icon: '3', // 使用数据库图标作为主机图标
          tag: '主机',
          nodeType: 'host',
          ipAddress: hostData.ipAddress,
          operatingSystem: hostData.operatingSystem,
          diskSpace: hostData.diskSpace,
          memory: hostData.memory,
          cpuCores: hostData.cpuCores,
        },
      }

      // 添加新节点到画布
      setNodes((nds) => nds.concat(newNode))

      // 关闭对话框
      setIsCreateHostDialogOpen(false)

      toast.success('主机节点已创建')
    } catch (error) {
      console.error('创建主机节点失败:', error)
      toast.error('创建主机节点失败，请重试')
    }
  }, [])

  // 保存主机节点编辑
  const saveHostNodeEdit = useCallback((updatedData: {
    label: string;
    description: string;
    ipAddress: string;
    operatingSystem: string;
    diskSpace: string;
    memory: string;
    cpuCores: string;
  }) => {
    if (selectedHostNode) {
      setNodes((nds) =>
        nds.map((node) => {
          if (node.id === selectedHostNode.id) {
            return {
              ...node,
              data: {
                ...node.data,
                label: updatedData.label,
                description: updatedData.description,
                ipAddress: updatedData.ipAddress,
                operatingSystem: updatedData.operatingSystem,
                diskSpace: updatedData.diskSpace,
                memory: updatedData.memory,
                cpuCores: updatedData.cpuCores,
              },
            }
          }
          return node
        })
      )
      toast.success('主机节点已保存')
    }
  }, [selectedHostNode, setNodes])

  // 创建节点分组
  const createNodeGroup = useCallback(() => {
    if (selectedNodes.length < 2) {
      toast.error('请至少选择两个节点来创建分组');
      return;
    }

    // 找出选中节点的边界
    const selectedNodeObjects = nodes.filter(node => selectedNodes.includes(node.id));

    let minX = Infinity;
    let minY = Infinity;
    let maxX = -Infinity;
    let maxY = -Infinity;

    selectedNodeObjects.forEach(node => {
      const x = node.position.x;
      const y = node.position.y;
      const width = 280; // 假设节点宽度
      const height = 100; // 假设节点高度

      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
      maxX = Math.max(maxX, x + width);
      maxY = Math.max(maxY, y + height);
    });

    // 添加一些边距
    const padding = 30;
    minX -= padding;
    minY -= padding;
    maxX += padding;
    maxY += padding;

    // 创建分组节点
    const groupNode: Node = {
      id: `group-${Date.now()}`,
      type: 'group',
      position: { x: minX, y: minY },
      data: {
        label: groupName,
        nodes: selectedNodes,
        width: maxX - minX,
        height: maxY - minY,
        color: 'rgba(240, 240, 240, 0.6)',
      },
      zIndex: -1, // 确保分组在节点下方
    };

    setNodes(nds => {
      // 添加分组节点，同时清除所有节点的选中状态
      return [...nds.map(n => ({ ...n, selected: false })), groupNode];
    });
    setGroupName('新分组');
    setSelectedNodes([]);
    setIsCreateGroupDialogOpen(false);
    toast.success('节点分组创建成功');
  }, [selectedNodes, nodes, groupName, setNodes]);

  return (
    <>
      <Header fixed>
        <Search />
        <div className="ml-auto flex items-center gap-4">
          <ThemeSwitch />
          <LanguageSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main fixed>
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold tracking-tight">{t.deploymentPlan.title}</h1>
        </div>

        <div className="flex gap-4 h-[calc(100vh-140px)]">
          {/* 左侧面板 */}
          {isLeftPanelOpen && (
            <div className="w-[450px] flex-shrink-0 rounded-xl bg-background shadow-md border border-gray-200 dark:border-gray-700">
              <DeploymentConfigPanel
                isOpen={isLeftPanelOpen}
                onClose={() => setIsLeftPanelOpen(false)}
              />
            </div>
          )}

          {/* 中间画布 */}
          <div className="flex-1 rounded-xl bg-background shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div
              className="h-full w-full"
              ref={reactFlowWrapper}
            >
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onNodeClick={onNodeClick}
              onNodeDoubleClick={onNodeDoubleClick}
              onPaneClick={onPaneClick}
              nodeTypes={nodeTypes}
              deleteKeyCode="Delete"
              onInit={(instance) => { reactFlowInstance.current = instance }}
              fitView={true}
              fitViewOptions={{ padding: 0.9 }}
              defaultViewport={{ x: 0, y: 0, zoom: 0.9 }}
              panOnScroll={false}
              selectionOnDrag={interactionMode === 'select'}
              panOnDrag={interactionMode === 'pan'}
              zoomOnScroll={false}
              zoomOnPinch={true}
              zoomOnDoubleClick={false}
              minZoom={0.3}
              maxZoom={2.5}
              defaultEdgeOptions={{
                animated: true,
                type: 'smoothstep',
                markerEnd: undefined,
                markerStart: undefined,
                style: {
                  strokeWidth: 2
                }
              }}
              connectionLineType={"smoothstep" as any}
              connectionLineStyle={{
                stroke: 'rgba(0, 0, 0, 0.7)',
                strokeWidth: 2.5,
                strokeDasharray: getConnectionLineProperties().strokeDasharray
              }}
              connectionMode={"loose" as any} // 使用宽松模式，更容易创建连接
              connectionRadius={50} // 增加连接半径，使连接更容易
              snapToGrid={true} // 启用网格对齐
              snapGrid={[20, 20]} // 设置网格大小
              elevateEdgesOnSelect={true} // 选中时提升边的层级
              className={`${interactionMode === 'pan' ? "pan-mode" : "select-mode"} ${toolMode === 'connect' ? "connect-mode" : ""}`}
            >
              {/* 自定义箭头标记定义 */}
              <svg style={{ position: 'absolute', top: 0, left: 0, width: 0, height: 0 }}>
                <defs>
                  {/* 空心箭头 */}
                  <marker
                    id="arrow-open"
                    viewBox="0 0 10 10"
                    refX="9"
                    refY="5"
                    markerWidth="6"
                    markerHeight="6"
                    orient="auto-start-reverse"
                  >
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="none" stroke="rgba(0, 0, 0, 0.7)" strokeWidth="1" />
                  </marker>

                  {/* 实心箭头 */}
                  <marker
                    id="arrow-filled"
                    viewBox="0 0 10 10"
                    refX="9"
                    refY="5"
                    markerWidth="6"
                    markerHeight="6"
                    orient="auto-start-reverse"
                  >
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="rgba(0, 0, 0, 0.7)" />
                  </marker>
                </defs>
              </svg>
              <Controls />
              <Background variant={"dots" as any} gap={12} size={1} />


              {/* 底部工具栏 */}
              <Panel position="bottom-center" className="mb-6">
                <div className="flex flex-row gap-3 px-8 py-5 rounded-xl toolbar-container bg-black border border-gray-800 shadow-xl">
                  {/* 左侧面板切换按钮 */}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setIsLeftPanelOpen(!isLeftPanelOpen)}
                    className={`h-14 w-14 transition-all duration-200 hover:bg-gray-700 hover:shadow-md hover:scale-105 active:scale-95 toolbar-button ${isLeftPanelOpen ? 'bg-primary/40 border border-primary/60 shadow-inner' : 'bg-gray-800'}`}
                    title={isLeftPanelOpen ? "隐藏左侧面板" : "显示左侧面板"}
                  >
                    <IconLayoutSidebar className={`toolbar-icon text-white ${isLeftPanelOpen ? 'text-primary' : ''}`} size={24} />
                  </Button>

                  {/* 交互模式切换按钮 */}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setInteractionMode(interactionMode === 'pan' ? 'select' : 'pan')}
                    className={`h-14 w-14 transition-all duration-200 hover:bg-gray-700 hover:shadow-md hover:scale-105 active:scale-95 toolbar-button ${interactionMode === 'select' ? 'bg-primary/40 border border-primary/60 shadow-inner' : 'bg-gray-800'}`}
                    title={interactionMode === 'select' ? "切换到拖动模式" : "切换到选择模式"}
                  >
                    {interactionMode === 'select' ? (
                      <IconPointer className="toolbar-icon text-primary" size={24} />
                    ) : (
                      <IconHandGrab className="toolbar-icon text-white" size={24} />
                    )}
                  </Button>

                  {/* 添加应用按钮 */}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setIsAddAppDialogOpen(true)}
                    className="h-14 w-14 transition-all duration-200 hover:bg-gray-700 hover:shadow-md hover:scale-105 active:scale-95 bg-gray-800 toolbar-button"
                    title={t.deploymentPlan.addApp}
                  >
                    <IconApps className="toolbar-icon text-white" size={24} />
                  </Button>

                  {/* 创建节点按钮 */}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      // 设置默认值
                      setNodeName(`新节点 ${nodes.length + 1}`)
                      setNodeDescription('自定义节点描述')
                      setNodeTag('自定义')
                      setNodeIcon('2')

                      // 设置默认位置（画布中心）
                      if (reactFlowInstance.current) {
                        const { x, y, zoom } = reactFlowInstance.current.getViewport();
                        const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();

                        if (reactFlowBounds) {
                          // 计算画布中心点
                          const centerX = (reactFlowBounds.width / 2 - x) / zoom;
                          const centerY = (reactFlowBounds.height / 2 - y) / zoom;

                          // 添加一点随机偏移，避免节点完全重叠
                          setClickPosition({
                            x: centerX + (Math.random() * 100 - 50),
                            y: centerY + (Math.random() * 100 - 50)
                          });
                        } else {
                          // 如果无法获取边界，使用默认位置
                          setClickPosition({ x: 200, y: 200 });
                        }
                      } else {
                        // 如果无法获取视口，使用默认位置
                        setClickPosition({ x: 200, y: 200 });
                      }

                      // 打开创建节点对话框
                      setIsCreateNodeDialogOpen(true)
                    }}
                    className="h-14 w-14 transition-all duration-200 hover:bg-gray-700 hover:shadow-md hover:scale-105 active:scale-95 bg-gray-800 toolbar-button"
                    title={t.deploymentPlan.createNode}
                  >
                    <IconCode className="toolbar-icon text-white" size={24} />
                  </Button>

                  {/* 创建主机节点按钮 */}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setIsCreateHostDialogOpen(true)}
                    className="h-14 w-14 transition-all duration-200 hover:bg-gray-700 hover:shadow-md hover:scale-105 active:scale-95 bg-gray-800 toolbar-button"
                    title="创建主机节点"
                  >
                    <IconServer className="toolbar-icon text-white" size={24} />
                  </Button>

                  {/* 连线工具按钮 */}
                  <div className="relative">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => {
                        if (toolMode === 'connect') {
                          // 如果已经在连线模式，则退出连线模式
                          setToolMode('none');
                          setConnectionStart(null);
                          toast.info('已退出连线模式');
                        } else {
                          // 进入连线模式
                          setToolMode('connect');
                          toast.info('已进入连线模式，请先点击起始节点，再点击目标节点');
                        }
                        // 切换连线样式面板的显示状态
                        setIsLineStylePanelOpen(!isLineStylePanelOpen);
                      }}
                      className={`h-14 w-14 transition-all duration-200 hover:bg-gray-700 hover:shadow-md hover:scale-105 active:scale-95 toolbar-button ${toolMode === 'connect' ? 'bg-primary/40 border border-primary/60 shadow-inner' : 'bg-gray-800'}`}
                      title={toolMode === 'connect' ? "退出连线模式" : "进入连线模式"}
                    >
                      <IconLine className={`toolbar-icon ${toolMode === 'connect' ? 'text-primary' : 'text-white'}`} size={24} />
                    </Button>

                    {/* 连线样式选择面板 */}
                    {isLineStylePanelOpen && (
                      <div className="absolute bottom-full mb-2 left-0 bg-white dark:bg-gray-800 shadow-md rounded-md p-2 z-50 border border-gray-200 dark:border-gray-700 w-[200px]">
                        <h3 className="text-xs font-semibold mb-2 px-2">选择连线样式</h3>
                        <div className="space-y-1">
                          <button
                            className={`w-full text-left px-2 py-1.5 text-xs rounded-sm flex items-center gap-2 ${lineStyle === 'dashed' ? 'bg-primary/20' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}`}
                            onClick={() => {
                              setLineStyle('dashed');
                              toast.info('已选择虚线样式');
                            }}
                          >
                            <svg width="40" height="6" viewBox="0 0 40 6">
                              <path d="M0,3 L40,3" stroke="black" strokeWidth="2" strokeDasharray="5,3" />
                            </svg>
                            <span>虚线</span>
                          </button>

                          <button
                            className={`w-full text-left px-2 py-1.5 text-xs rounded-sm flex items-center gap-2 ${lineStyle === 'dashed-arrow-open' ? 'bg-primary/20' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}`}
                            onClick={() => {
                              setLineStyle('dashed-arrow-open');
                              toast.info('已选择带空心箭头的虚线样式');
                            }}
                          >
                            <svg width="40" height="6" viewBox="0 0 40 6">
                              <path d="M0,3 L35,3" stroke="black" strokeWidth="2" strokeDasharray="5,3" />
                              <path d="M35,3 L40,3" stroke="black" strokeWidth="2" />
                              <path d="M35,1 L39,3 L35,5" stroke="black" strokeWidth="1" fill="none" />
                            </svg>
                            <span>虚线 + 空心箭头</span>
                          </button>

                          <button
                            className={`w-full text-left px-2 py-1.5 text-xs rounded-sm flex items-center gap-2 ${lineStyle === 'solid-arrow-open' ? 'bg-primary/20' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}`}
                            onClick={() => {
                              setLineStyle('solid-arrow-open');
                              toast.info('已选择带空心箭头的实线样式');
                            }}
                          >
                            <svg width="40" height="6" viewBox="0 0 40 6">
                              <path d="M0,3 L35,3" stroke="black" strokeWidth="2" />
                              <path d="M35,1 L39,3 L35,5" stroke="black" strokeWidth="1" fill="none" />
                            </svg>
                            <span>实线 + 空心箭头</span>
                          </button>

                          <button
                            className={`w-full text-left px-2 py-1.5 text-xs rounded-sm flex items-center gap-2 ${lineStyle === 'dashed-arrow-filled' ? 'bg-primary/20' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}`}
                            onClick={() => {
                              setLineStyle('dashed-arrow-filled');
                              toast.info('已选择带实心箭头的虚线样式');
                            }}
                          >
                            <svg width="40" height="6" viewBox="0 0 40 6">
                              <path d="M0,3 L35,3" stroke="black" strokeWidth="2" strokeDasharray="5,3" />
                              <path d="M35,1 L39,3 L35,5" stroke="black" strokeWidth="1" fill="black" />
                            </svg>
                            <span>虚线 + 实心箭头</span>
                          </button>

                          <button
                            className={`w-full text-left px-2 py-1.5 text-xs rounded-sm flex items-center gap-2 ${lineStyle === 'solid-arrow-filled' ? 'bg-primary/20' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}`}
                            onClick={() => {
                              setLineStyle('solid-arrow-filled');
                              toast.info('已选择带实心箭头的实线样式');
                            }}
                          >
                            <svg width="40" height="6" viewBox="0 0 40 6">
                              <path d="M0,3 L35,3" stroke="black" strokeWidth="2" />
                              <path d="M35,1 L39,3 L35,5" stroke="black" strokeWidth="1" fill="black" />
                            </svg>
                            <span>实线 + 实心箭头</span>
                          </button>
                        </div>
                      </div>
                    )}
                  </div>

                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      if (selectedNodes.length < 2) {
                        toast.error('请先按住 Ctrl 键选择至少两个节点');
                      } else {
                        setIsCreateGroupDialogOpen(true);
                      }
                    }}
                    className="h-14 w-14 transition-all duration-200 hover:bg-gray-700 hover:shadow-md hover:scale-105 active:scale-95 bg-gray-800 toolbar-button"
                    title="创建节点分组"
                  >
                    <IconLayersSubtract className="toolbar-icon text-white" size={24} />
                  </Button>

                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleSavePlan}
                    disabled={isSaving}
                    className="h-14 w-14 transition-all duration-200 hover:bg-gray-700 hover:shadow-md hover:scale-105 active:scale-95 bg-gray-800 toolbar-button"
                    title={t.deploymentPlan.savePlan}
                  >
                    {isSaving ? <Loader2 className="toolbar-icon text-white animate-spin" size={24} /> : <IconDeviceFloppy className="toolbar-icon text-white" size={24} />}
                  </Button>

                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      // 如果节点编辑面板打开，检查是否有未保存的更改
                      if (isNodeEditPanelOpen) {
                        // 获取当前选中的节点编辑组件的hasChanges状态
                        const nodeEditPanel = document.getElementById('node-edit-panel');
                        const hasChanges = nodeEditPanel?.getAttribute('data-has-changes') === 'true';

                        if (hasChanges) {
                          toast.info('当前编辑面板内容有改动，请先关闭编辑面板');
                          return;
                        } else {
                          // 如果没有更改，关闭节点编辑面板并打开AI架构师
                          setIsNodeEditPanelOpen(false);
                          setIsAIArchitectOpen(true);
                          return;
                        }
                      }

                      // 如果主机节点编辑面板打开，检查是否有未保存的更改
                      if (isHostNodeEditPanelOpen) {
                        // 获取当前选中的主机节点编辑组件的hasChanges状态
                        const hostNodeEditPanel = document.getElementById('host-node-edit-panel');
                        const hasChanges = hostNodeEditPanel?.getAttribute('data-has-changes') === 'true';

                        if (hasChanges) {
                          toast.info('当前编辑面板内容有改动，请先关闭编辑面板');
                          return;
                        } else {
                          // 如果没有更改，关闭主机节点编辑面板并打开AI架构师
                          setIsHostNodeEditPanelOpen(false);
                          setIsAIArchitectOpen(true);
                          return;
                        }
                      }

                      // 如果没有编辑面板打开，则切换AI架构师面板
                      setIsAIArchitectOpen(!isAIArchitectOpen);
                    }}
                    className={`h-14 w-14 transition-all duration-200 hover:bg-gray-700 hover:shadow-md hover:scale-105 active:scale-95 toolbar-button ${isAIArchitectOpen ? 'bg-primary/40 border border-primary/60 shadow-inner' : 'bg-gray-800'}`}
                    title="AI 架构师"
                  >
                    <IconCircleDottedLetterA className={`toolbar-icon ${isAIArchitectOpen ? 'text-primary' : 'text-white'}`} size={24} />
                  </Button>

                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      toast.info(
                        <div className="text-xs">
                          <p className="font-bold mb-1">{t.deploymentPlan.tips}：</p>
                          <ul className="list-disc pl-4">
                            <li><strong>创建节点</strong> - 按住 Shift 键点击画布创建新节点</li>
                            <li><strong>选中节点</strong> - 单击节点可以选中节点</li>
                            <li><strong>编辑节点</strong> - 双击节点可以在右侧打开节点编辑面板</li>
                            <li><strong>删除节点</strong> - 选中节点后按 Delete 键删除</li>
                            <li><strong>连接节点</strong> - 点击底部工具栏中的连线工具，然后依次点击两个节点即可创建连接</li>
                            <li><strong>连接点连接</strong> - 悬停在节点上会显示连接点，从任意连接点拖动到另一个节点的任意连接点即可创建连接</li>
                            <li><strong>快速连接</strong> - 选中一个节点后，按住 Alt 键并点击另一个节点，可以快速创建连接</li>
                            <li><strong>灵活连接</strong> - 节点四周都有连接点，可以从任意方向连接到任意方向</li>
                            <li><strong>移动节点</strong> - 直接拖动节点可以改变位置</li>
                            <li><strong>交互模式</strong> - 底部工具栏可以点击图标切换拖动模式（小手图标）和选择模式（箭头图标）</li>
                            <li><strong>拖动画布</strong> - 在拖动模式下，鼠标显示为小手，可以拖动空白区域移动整个画布</li>
                            <li><strong>创建分组</strong> - 按住 Ctrl 键选择多个节点，然后点击分组按钮将它们组合在一起</li>
                            <li><strong>记得保存</strong> - 修改后点击保存按钮保存更改</li>
                          </ul>
                        </div>,
                        {
                          duration: 10000,
                        }
                      )
                    }}
                    className="h-14 w-14 transition-all duration-200 hover:bg-gray-700 hover:shadow-md hover:scale-105 active:scale-95 bg-gray-800 toolbar-button"
                    title={t.deploymentPlan.tips}
                  >
                    <IconInfoCircle className="toolbar-icon text-white" size={24} />
                  </Button>
                </div>
              </Panel>
            </ReactFlow>
          </div>
        </div>

          {/* 右侧面板 */}
          {(isAIArchitectOpen || isNodeEditPanelOpen || isHostNodeEditPanelOpen) && (
            <div className="w-[450px] flex-shrink-0 rounded-xl bg-background shadow-md border border-gray-200 dark:border-gray-700">
              <div className="h-full p-4 overflow-auto">
                {isAIArchitectOpen && (
                  <AIArchitect
                    isOpen={isAIArchitectOpen}
                    onClose={() => setIsAIArchitectOpen(false)}
                  />
                )}

                {(isNodeEditPanelOpen || isHostNodeEditPanelOpen) && (
                  <NodeEditPanel
                    isOpen={isNodeEditPanelOpen || isHostNodeEditPanelOpen}
                    onClose={() => {
                      setIsNodeEditPanelOpen(false)
                      setIsHostNodeEditPanelOpen(false)
                    }}
                    selectedNode={isHostNodeEditPanelOpen ? selectedHostNode : selectedNode}
                    onSave={isHostNodeEditPanelOpen ? saveHostNodeEdit : saveNodeEdit}
                  />
                )}
              </div>
            </div>
          )}
        </div>

        {/* 创建主机对话框 */}
        <CreateHostDialog
          isOpen={isCreateHostDialogOpen}
          onOpenChange={setIsCreateHostDialogOpen}
          onCreateHost={createHostNode}
        />
      </Main>

      {/* 添加应用对话框 */}
      <Dialog
        open={isAddAppDialogOpen}
        onOpenChange={(open) => {
          setIsAddAppDialogOpen(open)
          // 如果对话框打开，则刷新应用数据
          if (open) {
            // 使用 queryClient 手动刷新数据
            queryClient.invalidateQueries({ queryKey: ['deployment-plan-apps'] })
          }
        }}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{t.deploymentPlan.addApp}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4 max-h-[60vh] overflow-y-auto">
            {isLoading ? (
              <div className="flex justify-center items-center py-20">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                {t.apps.loading}
              </div>
            ) : isError ? (
              <div className="flex justify-center items-center py-20">
                <p className="text-destructive">{t.apps.error}</p>
              </div>
            ) : appsData && Array.isArray(appsData) && appsData.length > 0 ? (
              <div className="grid grid-cols-2 gap-4">
                {appsData.map((app: App) => (
                  <Card
                    key={app.id}
                    className="cursor-pointer hover:shadow-md transition-shadow border hover:border-primary"
                    onClick={() => addAppToCanvas(app)}
                  >
                    <CardHeader className="pb-2">
                      <div className="flex items-center gap-3">
                        <div className="flex size-10 items-center justify-center rounded-lg p-2 bg-gray-50 dark:bg-gray-800">
                          <div className={`text-2xl ${getIconColor(app.logo as string)}`}>
                            {getIconById(app.logo as string)}
                          </div>
                        </div>
                        <CardTitle className="text-base">{app.name}</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-500 line-clamp-2">{app.desc}</p>
                      <div className="mt-2 flex justify-between items-center">
                        <span className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-md">
                          {app.tag}
                        </span>
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={(e) => {
                            e.stopPropagation()
                            addAppToCanvas(app)
                          }}
                        >
                          {t.deploymentPlan.addToCanvas}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <p className="text-center py-10">{t.deploymentPlan.noApps}</p>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* 编辑节点对话框 */}
      <Dialog open={isEditNodeDialogOpen} onOpenChange={setIsEditNodeDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{t.deploymentPlan.editNode}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="nodeName" className="text-right">
                {t.deploymentPlan.nodeName}
              </Label>
              <Input
                id="nodeName"
                value={nodeName}
                onChange={(e) => setNodeName(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="nodeDescription" className="text-right">
                {t.deploymentPlan.nodeDescription}
              </Label>
              <Textarea
                id="nodeDescription"
                value={nodeDescription}
                onChange={(e) => setNodeDescription(e.target.value)}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditNodeDialogOpen(false)}>
              {t.deploymentPlan.cancel}
            </Button>
            <Button onClick={() => saveNodeEdit()}>{t.deploymentPlan.saveNode}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 创建节点对话框 */}
      <Dialog open={isCreateNodeDialogOpen} onOpenChange={setIsCreateNodeDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{t.deploymentPlan.createNode}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="createNodeName" className="text-right">
                {t.deploymentPlan.nodeName}
              </Label>
              <Input
                id="createNodeName"
                value={nodeName}
                onChange={(e) => setNodeName(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="createNodeDescription" className="text-right">
                {t.deploymentPlan.nodeDescription}
              </Label>
              <Textarea
                id="createNodeDescription"
                value={nodeDescription}
                onChange={(e) => setNodeDescription(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="createNodeTag" className="text-right">
                {t.deploymentPlan.nodeTag}
              </Label>
              <Input
                id="createNodeTag"
                value={nodeTag}
                onChange={(e) => setNodeTag(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="createNodeIcon" className="text-right">
                {t.deploymentPlan.nodeIcon}
              </Label>
              <div className="col-span-3 flex gap-2 flex-wrap">
                {Array.from({ length: 12 }, (_, i) => i + 1).map((num) => (
                  <div
                    key={num}
                    className={`cursor-pointer p-2 rounded-md ${nodeIcon === String(num) ? 'bg-primary/20 border-2 border-primary' : 'bg-gray-100 dark:bg-gray-800'}`}
                    onClick={() => setNodeIcon(String(num))}
                  >
                    <div className={`text-2xl ${getIconColor(String(num))}`}>
                      {getIconById(String(num))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateNodeDialogOpen(false)}>
              {t.deploymentPlan.cancel}
            </Button>
            <Button onClick={createNewNode}>{t.deploymentPlan.createNode}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 创建分组对话框 */}
      <Dialog open={isCreateGroupDialogOpen} onOpenChange={setIsCreateGroupDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>创建节点分组</DialogTitle>
            <DialogDescription>
              已选择 {selectedNodes.length} 个节点，创建分组将这些节点视觉上组合在一起。
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="groupName" className="text-right">
                分组名称
              </Label>
              <Input
                id="groupName"
                value={groupName}
                onChange={(e) => setGroupName(e.target.value)}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateGroupDialogOpen(false)}>
              {t.deploymentPlan.cancel}
            </Button>
            <Button onClick={createNodeGroup}>创建分组</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}




