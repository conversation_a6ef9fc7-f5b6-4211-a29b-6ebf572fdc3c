const cn = {
  login:{
    success: "登录成功",
  },
  Sidebar: {
    general: "通用",
    dashboard: "首页",
    tasks: "任务",
    app: "应用",
    deploymentPlan: "部署方案",
    chat: "聊天",
    ai: "AI",
    user: "用户",
    other: "其他",
    settings: "设置",
    profile: "个人",
    account: "账号",
    appearance: "外观",
    notifications: "通知",
    display: "显示",
    helpCenter: "帮助中心"
  },
  ai:{
    history: "历史对话",
    newChat: "新对话",
    placeholder: "给AISecOps发送消息(Ctrl+Enter换行, Enter发送)",
    send: "发送",
    greeting: "你好",
    helpQuestion: "今天我能帮你什么？",
  },
  apps: {
    title: "应用市场",
    filter: "筛选应用...",
    all: "所有应用",
    deployed: "已集成",
    notDeployed: "未集成",
    ascending: "升序",
    descending: "降序",
    deploy: "集成",
    deployed_status: "已集成",
    error: "加载应用失败，请稍后再试。",
    tool: "工具",
    blog: "博客",
    ai: "AI",
    smartSelection: "智选",
    loading: "数据加载中..."
  },
  deploymentPlan: {
    title: "部署方案",
    addApp: "添加应用",
    editNode: "编辑节点",
    saveNode: "保存",
    cancel: "取消",
    nodeName: "节点名称",
    nodeDescription: "节点描述",
    addToCanvas: "添加到画布",
    noApps: "暂无应用，请先添加应用",
    connectionError: "连接失败，请重试",
    connectionExists: "连接已存在",
    connectionSuccess: "连接创建成功",
    connectionTip: "选中一个节点，然后按住 Alt 键并点击另一个节点即可创建连接",
    savePlan: "保存方案",
    planSaved: "部署方案已保存",
    planSaveFailed: "部署方案保存失败",
    appAdded: "应用已添加到画布",
    createNode: "创建节点",
    nodeCreated: "节点已创建",
    nodeCreateTip: "按住 Shift 键并点击画布创建新节点",
    tips: "提示",
    tipShiftClick: "按住 Shift 点击画布创建节点",
    tipClickNode: "点击节点可编辑",
    tipDeleteNode: "按 Delete 键删除选中节点",
    tipDragConnect: "拖动节点连接点创建连线",
    nodeTag: "标签",
    nodeIcon: "图标",
    addToDeploymentPlan: "添加到部署方案",
    integrate: "集成",
    addedToDeploymentPlan: "已添加到部署方案",
    viewDeploymentPlan: "前往查看？",
    addToDeploymentPlanFailed: "添加到部署方案失败"
  }
};

export type LocaleType = typeof cn;
export default cn;



