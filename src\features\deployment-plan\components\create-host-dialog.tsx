import { useState } from 'react'
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'

interface CreateHostDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateHost: (hostData: {
    name: string;
    description: string;
    ipAddress: string;
    operatingSystem: string;
    diskSpace: string;
    memory: string;
    cpuCores: string;
  }) => void;
}

export default function CreateHostDialog({ isOpen, onOpenChange, onCreateHost }: CreateHostDialogProps) {
  const [hostName, setHostName] = useState('新主机')
  const [hostDescription, setHostDescription] = useState('')
  const [ipAddress, setIpAddress] = useState('')
  const [operatingSystem, setOperatingSystem] = useState('')
  const [diskSpace, setDiskSpace] = useState('')
  const [memory, setMemory] = useState('')
  const [cpuCores, setCpuCores] = useState('')

  const handleCreateHost = () => {
    // 验证必填字段
    if (!ipAddress.trim()) {
      alert('请输入IP地址');
      return;
    }

    if (!hostName.trim()) {
      alert('请输入主机名称');
      return;
    }

    onCreateHost({
      name: hostName,
      description: hostName, // 使用主机名称作为描述
      ipAddress,            // IP地址将作为节点名称
      operatingSystem,
      diskSpace,
      memory,
      cpuCores
    })

    // 重置表单
    setHostName('新主机')
    setHostDescription('')
    setIpAddress('')
    setOperatingSystem('')
    setDiskSpace('')
    setMemory('')
    setCpuCores('')
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>创建主机节点</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="hostName" className="text-right">
              主机名称 <span className="text-red-500">*</span>
            </Label>
            <Input
              id="hostName"
              value={hostName}
              onChange={(e) => setHostName(e.target.value)}
              className="col-span-3"
              required
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="ipAddress" className="text-right">
              IP地址 <span className="text-red-500">*</span>
            </Label>
            <Input
              id="ipAddress"
              value={ipAddress}
              onChange={(e) => setIpAddress(e.target.value)}
              placeholder="例如: *************"
              className="col-span-3"
              required
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="operatingSystem" className="text-right">
              操作系统
            </Label>
            <Input
              id="operatingSystem"
              value={operatingSystem}
              onChange={(e) => setOperatingSystem(e.target.value)}
              placeholder="例如: Windows Server 2022, Ubuntu 22.04"
              className="col-span-3"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="cpuCores" className="text-right">
              CPU核心数
            </Label>
            <div className="col-span-3 flex items-center">
              <Input
                id="cpuCores"
                value={cpuCores}
                onChange={(e) => setCpuCores(e.target.value)}
                placeholder="例如: 4"
                className="flex-1"
              />
              <span className="ml-2 text-sm text-gray-500">核</span>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="memory" className="text-right">
              内存容量
            </Label>
            <div className="col-span-3 flex items-center">
              <Input
                id="memory"
                value={memory}
                onChange={(e) => setMemory(e.target.value)}
                placeholder="例如: 16"
                className="flex-1"
              />
              <span className="ml-2 text-sm text-gray-500">GB</span>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="diskSpace" className="text-right">
              磁盘容量
            </Label>
            <div className="col-span-3 flex items-center">
              <Input
                id="diskSpace"
                value={diskSpace}
                onChange={(e) => setDiskSpace(e.target.value)}
                placeholder="例如: 500"
                className="flex-1"
              />
              <span className="ml-2 text-sm text-gray-500">GB</span>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="hostDescription" className="text-right">
              备注说明
            </Label>
            <Textarea
              id="hostDescription"
              value={hostDescription}
              onChange={(e) => setHostDescription(e.target.value)}
              placeholder="输入主机描述（可选）"
              className="col-span-3"
              rows={3}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleCreateHost}>创建主机</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
