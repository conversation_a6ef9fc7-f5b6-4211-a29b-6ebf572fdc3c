import { memo } from 'react'
import { Handle, Position, NodeProps } from '@xyflow/react'
import { getIconById, getIconColor } from '@/features/apps/utils/icon-utils'

interface CustomNodeData {
  label: string;
  description?: string;
  icon?: string;
  tag?: string;
}

const CustomNode = ({ data }: NodeProps) => {
  // 确保 data 符合预期的类型
  const nodeData = data as unknown as CustomNodeData;
  console.log('CustomNode rendering with data:', data)

  return (
    <div className="group px-4 py-3 shadow-md rounded-md bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 w-[280px] transition-all duration-200 hover:shadow-lg hover:border-primary/30 dark:hover:border-primary/30 handle-container">
      <div className="flex items-center">
        <div className="flex size-10 items-center justify-center rounded-lg p-2 bg-gray-50 dark:bg-gray-700 flex-shrink-0">
          <div className={`text-2xl ${getIconColor(nodeData.icon || '1')}`}>
            {getIconById(nodeData.icon || '1')}
          </div>
        </div>
        <div className="ml-3 overflow-hidden">
          <div className="text-lg font-medium truncate">{nodeData.label}</div>
          {nodeData.tag && (
            <div className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-md inline-block mt-1">
              {nodeData.tag}
            </div>
          )}
        </div>
      </div>
      {nodeData.description && (
        <div className="mt-2 text-sm text-gray-500 dark:text-gray-400 line-clamp-3 overflow-hidden text-ellipsis" title={nodeData.description}>
          {nodeData.description}
        </div>
      )}

      {/* 连接点 - 上下左右四个方向 */}

      {/* 顶部连接点 */}
      <Handle
        type="source"
        position={Position.Top}
        style={{ top: -8 }}
        id="top"
        title="拖动此点创建连接"
        className="top-handle"
      />

      {/* 底部连接点 */}
      <Handle
        type="source"
        position={Position.Bottom}
        style={{ bottom: -8 }}
        id="bottom"
        title="拖动此点创建连接"
        className="bottom-handle"
      />

      {/* 左侧连接点 */}
      <Handle
        type="source"
        position={Position.Left}
        style={{ left: -8, top: '50%' }}
        id="left"
        title="拖动此点创建连接"
        className="left-handle"
      />

      {/* 右侧连接点 */}
      <Handle
        type="source"
        position={Position.Right}
        style={{ right: -8, top: '50%' }}
        id="right"
        title="拖动此点创建连接"
        className="right-handle"
      />
    </div>
  )
}

export default memo(CustomNode)
