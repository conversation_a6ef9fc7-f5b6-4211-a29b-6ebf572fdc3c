import { useState } from 'react'
import { IconChartBar } from '@tabler/icons-react'
import { Loader2, PanelRightIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { Separator } from '@/components/ui/separator'

interface AIArchitectProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function AIArchitect({ isOpen, onClose }: AIArchitectProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [scoreResult, setScoreResult] = useState<string | null>(null)

  if (!isOpen) return null

  const handleArchitectureScore = () => {
    setIsLoading(true)

    // 模拟 API 调用
    setTimeout(() => {
      setScoreResult(`
## 架构评分: 85/100

### 优势
- 数据库与应用服务分离，符合最佳实践
- 使用了合适的技术栈组合
- 系统结构清晰

### 改进建议
- 考虑添加负载均衡器提高可用性
- 建议增加监控组件
- 可以考虑引入缓存层提升性能

### 详细分析
当前架构采用了三层结构，基本满足需求。建议考虑引入更多的安全措施，例如WAF和入侵检测系统。

数据流向合理，但缺少数据备份和恢复策略。建议添加定期备份机制和灾难恢复计划。
      `)
      setIsLoading(false)
    }, 2000)
  }

  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-6 pb-3 border-b border-b-gray-200 dark:border-b-gray-700">
        <div className="flex items-center">
          <Button
            data-sidebar="trigger"
            data-slot="sidebar-trigger"
            variant="outline"
            size="icon"
            className={cn('size-7', 'scale-125 sm:scale-100', 'mr-3')}
            onClick={onClose}
          >
            <PanelRightIcon />
            <span className="sr-only">Toggle Sidebar</span>
          </Button>
          <Separator orientation="vertical" className="h-4 mr-3" />
          <h3 className="text-lg font-medium">AI 架构师</h3>
        </div>
      </div>

      <div className="flex-1 overflow-auto">
        {!scoreResult ? (
          <div className="flex flex-col items-center justify-center h-full">
            <p className="text-muted-foreground mb-8">AI 架构师助手可以帮助您评估当前的部署方案</p>
          </div>
        ) : (
          <div className="p-4">
            <div className="prose dark:prose-invert max-w-none">
              <div className="mb-6">
                <h2 className="text-xl font-bold">架构评分: 85/100</h2>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mt-2">
                  <div className="bg-primary h-2.5 rounded-full" style={{ width: '85%' }}></div>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-semibold">优势</h3>
                <ul className="mt-2">
                  <li>数据库与应用服务分离，符合最佳实践</li>
                  <li>使用了合适的技术栈组合</li>
                  <li>系统结构清晰</li>
                </ul>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-semibold">改进建议</h3>
                <ul className="mt-2">
                  <li>考虑添加负载均衡器提高可用性</li>
                  <li>建议增加监控组件</li>
                  <li>可以考虑引入缓存层提升性能</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold">详细分析</h3>
                <p className="mt-2">
                  当前架构采用了三层结构，基本满足需求。建议考虑引入更多的安全措施，例如WAF和入侵检测系统。
                </p>
                <p className="mt-2">
                  数据流向合理，但缺少数据备份和恢复策略。建议添加定期备份机制和灾难恢复计划。
                </p>
              </div>
            </div>

            <div className="mt-8 flex justify-center">
              <Button
                onClick={() => {
                  setScoreResult(null)
                }}
                variant="outline"
                className="w-64"
              >
                重新评分
              </Button>
            </div>
          </div>
        )}
      </div>

      <div className="mt-auto pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex justify-center">
          <Button
            onClick={isLoading ? undefined : handleArchitectureScore}
            className={`h-10 w-64 text-base rounded-md flex items-center justify-center gap-2 transition-all duration-200 ease-out ${
              isLoading
                ? 'bg-gray-600 dark:bg-gray-700 text-white'
                : 'bg-primary text-primary-foreground hover:bg-primary/90'
            }`}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 size={18} className="mr-2 animate-spin" />
                评分中...
              </>
            ) : (
              <>
                <IconChartBar size={18} className="mr-2" />
                架构评分
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}
