import { FlowiseAgent } from '@/constant.ts'
import { SmartSelectionAgentApi } from '@/api/ai/SmartSelectionAgentApi.ts'

export const ROLES = ["apiMessage", "userMessage"] as const;
export type MessageRole = (typeof ROLES)[number];

export interface RequestMessage {
  role: MessageRole;
  content: string;
}

export interface FlowiseOptions{
  question: string;
  chatId: string;
  overrideConfig?:object;
  onUpdate?: (message: string) => void;
  onFinish: (message: string) => void;
  onError?: (err: Error) => void;
}


export abstract class FlowiseAgentApi{
  abstract chat(options: FlowiseOptions): Promise<void>;
}


export class FlowiseClientApi {
  public flowiseAgent: FlowiseAgentApi;
  public agentProvider: FlowiseAgent;

  constructor(agentProvider: FlowiseAgent) {
    switch (agentProvider) {
      case FlowiseAgent.SmartSelectionAgent:
        this.flowiseAgent = new SmartSelectionAgentApi();
        break;
      default:
        throw new Error("Unsupported agent provider");
    }
    this.agentProvider = agentProvider;
  }
}




