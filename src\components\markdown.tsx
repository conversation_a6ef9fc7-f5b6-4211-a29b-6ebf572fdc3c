import { Components } from "react-markdown";

const markdownComponents: Components = {
  code({ className, children, ...props }) {
    const match = /language-(\w+)/.exec(className || "");

    return match ? (
      <div className="relative w-full overflow-hidden">
        <pre className="bg-gray-900 p-4 rounded-lg overflow-x-auto w-full">
          <code className={`${className} text-sm whitespace-pre text-gray-100`} {...props}>
            {children}
          </code>
        </pre>
      </div>
    ) : (
      <code
        className="bg-gray-100 dark:bg-gray-800 px-1.5 py-0.5 rounded text-sm font-mono text-gray-900 dark:text-gray-100"
        {...props}
      >
        {children}
      </code>
    );
  },
  p({ children }) {
    return <p className="mb-4 last:mb-0">{children}</p>;
  },
  ul({ children }) {
    return <ul className="list-disc pl-6 mb-4 last:mb-0">{children}</ul>;
  },
  ol({ children }) {
    return <ol className="list-decimal pl-6 mb-4 last:mb-0">{children}</ol>;
  },
  li({ children }) {
    return <li className="mb-2 last:mb-0">{children}</li>;
  },
  h1({ children }) {
    return <h1 className="text-2xl font-bold mb-4 last:mb-0">{children}</h1>;
  },
  h2({ children }) {
    return <h2 className="text-xl font-bold mb-3 last:mb-0">{children}</h2>;
  },
  h3({ children }) {
    return <h3 className="text-lg font-bold mb-2 last:mb-0">{children}</h3>;
  },
  blockquote({ children }) {
    return (
      <blockquote className="border-l-4 border-gray-300 dark:border-gray-600 pl-4 italic my-4">
        {children}
      </blockquote>
    );
  },
  table({ children }) {
    return (
      <div className="overflow-x-auto my-4">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          {children}
        </table>
      </div>
    );
  },
  th({ children }) {
    return (
      <th className="px-6 py-3 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
        {children}
      </th>
    );
  },
  td({ children }) {
    return (
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
        {children}
      </td>
    );
  },
};

export default markdownComponents;
