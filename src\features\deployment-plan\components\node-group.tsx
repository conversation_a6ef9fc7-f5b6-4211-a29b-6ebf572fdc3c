import { memo, useState } from 'react'
import { NodeProps } from '@xyflow/react'

interface NodeGroupProps extends NodeProps {
  data: {
    label: string;
    nodes: string[]; // 组内节点的ID列表
    width: number;
    height: number;
    color?: string;
  };
  selected: boolean;
}

const NodeGroup = ({ data, selected }: NodeGroupProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [groupName, setGroupName] = useState(data.label || '节点组');

  // 计算分组的样式
  const groupStyle = {
    width: `${data.width}px`,
    height: `${data.height}px`,
    backgroundColor: data.color || 'rgba(240, 240, 240, 0.6)',
    border: selected 
      ? '2px dashed rgba(var(--primary-rgb), 0.7)' 
      : '2px dashed rgba(0, 0, 0, 0.2)',
    borderRadius: '8px',
    padding: '8px',
    position: 'relative' as const,
  };

  // 处理标题编辑完成
  const handleTitleBlur = () => {
    setIsEditing(false);
    // 这里可以添加更新分组标题的逻辑
  };

  return (
    <div 
      className="node-group transition-all duration-200"
      style={groupStyle}
      onDoubleClick={(e) => {
        e.stopPropagation();
        setIsEditing(true);
      }}
    >
      <div className="absolute top-0 left-0 transform -translate-y-1/2 bg-white dark:bg-gray-800 px-2 ml-2 rounded border border-gray-200 dark:border-gray-700">
        {isEditing ? (
          <input
            type="text"
            value={groupName}
            onChange={(e) => setGroupName(e.target.value)}
            onBlur={handleTitleBlur}
            onKeyDown={(e) => e.key === 'Enter' && handleTitleBlur()}
            className="text-sm p-1 outline-none border-none bg-transparent"
            autoFocus
          />
        ) : (
          <span className="text-sm font-medium">{groupName}</span>
        )}
      </div>
    </div>
  );
};

export default memo(NodeGroup);
