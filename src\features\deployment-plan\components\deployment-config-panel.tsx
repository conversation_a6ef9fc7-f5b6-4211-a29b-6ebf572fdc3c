import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { PanelLeftIcon } from 'lucide-react'
import { Separator } from '@/components/ui/separator'

interface DeploymentConfigPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function DeploymentConfigPanel({ isOpen, onClose }: DeploymentConfigPanelProps) {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  if (!isOpen) return null;

  const handleOptionSelect = (option: string) => {
    setSelectedOption(option);
  };

  return (
    <div className="h-full p-4 overflow-auto">
      <div className="flex items-center justify-between mb-6 pb-3 border-b border-b-gray-200 dark:border-b-gray-700">
        <div className="flex items-center">
          <Button
            data-sidebar="trigger"
            data-slot="sidebar-trigger"
            variant="outline"
            size="icon"
            className={cn('size-7', 'scale-125 sm:scale-100', 'mr-3')}
            onClick={onClose}
          >
            <PanelLeftIcon />
            <span className="sr-only">Toggle Sidebar</span>
          </Button>
          <Separator orientation="vertical" className="h-4 mr-3" />
          <h3 className="text-lg font-medium">部署配置</h3>
        </div>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Button
            variant={selectedOption === 'option1' ? 'default' : 'outline'}
            className="w-full justify-start"
            onClick={() => handleOptionSelect('option1')}
          >
            配置选项 1
          </Button>

          <Button
            variant={selectedOption === 'option2' ? 'default' : 'outline'}
            className="w-full justify-start"
            onClick={() => handleOptionSelect('option2')}
          >
            配置选项 2
          </Button>

          <Button
            variant={selectedOption === 'option3' ? 'default' : 'outline'}
            className="w-full justify-start"
            onClick={() => handleOptionSelect('option3')}
          >
            配置选项 3
          </Button>
        </div>

        <div className="pt-4">
          <h4 className="text-md font-medium mb-2">部署环境</h4>
          <div className="space-y-2">
            <Button
              variant={selectedOption === 'env1' ? 'default' : 'outline'}
              className="w-full justify-start"
              onClick={() => handleOptionSelect('env1')}
            >
              开发环境
            </Button>

            <Button
              variant={selectedOption === 'env2' ? 'default' : 'outline'}
              className="w-full justify-start"
              onClick={() => handleOptionSelect('env2')}
            >
              测试环境
            </Button>

            <Button
              variant={selectedOption === 'env3' ? 'default' : 'outline'}
              className="w-full justify-start"
              onClick={() => handleOptionSelect('env3')}
            >
              生产环境
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
