import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'
import { PanelLeftIcon, ChevronDownIcon, ChevronRightIcon, SearchIcon } from 'lucide-react'
import { Separator } from '@/components/ui/separator'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'

interface DeploymentConfigPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function DeploymentConfigPanel({ isOpen, onClose }: DeploymentConfigPanelProps) {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDeploymentOptionsOpen, setIsDeploymentOptionsOpen] = useState(true);
  const [isEnvironmentConfigOpen, setIsEnvironmentConfigOpen] = useState(true);

  if (!isOpen) return null;

  const handleOptionSelect = (option: string) => {
    setSelectedOption(option);
  };

  // 部署选项数据
  const deploymentOptions = [
    { id: 'docker', label: 'Docker 容器部署', description: '使用 Docker 容器化部署应用' },
    { id: 'kubernetes', label: 'Kubernetes 集群', description: '在 K8s 集群中部署应用' },
    { id: 'vm', label: '虚拟机部署', description: '传统虚拟机部署方式' },
    { id: 'serverless', label: 'Serverless 函数', description: '无服务器函数计算部署' },
    { id: 'microservices', label: '微服务架构', description: '微服务架构部署方案' },
  ];

  // 环境配置数据
  const environmentConfigs = [
    { id: 'dev', label: '开发环境', description: '用于开发和调试的环境' },
    { id: 'test', label: '测试环境', description: '用于功能测试和集成测试' },
    { id: 'staging', label: '预发布环境', description: '生产前的最终验证环境' },
    { id: 'prod', label: '生产环境', description: '正式对外提供服务的环境' },
  ];

  // 过滤搜索结果
  const filteredDeploymentOptions = deploymentOptions.filter(option =>
    option.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
    option.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredEnvironmentConfigs = environmentConfigs.filter(config =>
    config.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
    config.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="h-full p-4 overflow-auto">
      <div className="flex items-center justify-between mb-6 pb-3 border-b border-b-gray-200 dark:border-b-gray-700">
        <div className="flex items-center">
          <Button
            data-sidebar="trigger"
            data-slot="sidebar-trigger"
            variant="outline"
            size="icon"
            className={cn('size-7', 'scale-125 sm:scale-100', 'mr-3')}
            onClick={onClose}
          >
            <PanelLeftIcon />
            <span className="sr-only">Toggle Sidebar</span>
          </Button>
          <Separator orientation="vertical" className="h-4 mr-3" />
          <h3 className="text-lg font-medium">部署配置</h3>
        </div>
      </div>

      {/* 搜索框 */}
      <div className="mb-6">
        <div className="relative">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            type="text"
            placeholder="搜索配置选项..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <div className="space-y-4">
        {/* 部署选项 */}
        <Collapsible open={isDeploymentOptionsOpen} onOpenChange={setIsDeploymentOptionsOpen}>
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              className="w-full justify-between p-0 h-auto font-medium text-left hover:bg-transparent"
            >
              <span className="text-md font-medium">部署选项</span>
              {isDeploymentOptionsOpen ? (
                <ChevronDownIcon className="h-4 w-4" />
              ) : (
                <ChevronRightIcon className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-2 mt-3">
            {filteredDeploymentOptions.map((option) => (
              <div key={option.id} className="space-y-1">
                <Button
                  variant={selectedOption === option.id ? 'default' : 'outline'}
                  className="w-full justify-start h-auto p-3"
                  onClick={() => handleOptionSelect(option.id)}
                >
                  <div className="text-left">
                    <div className="font-medium">{option.label}</div>
                    <div className="text-xs text-muted-foreground mt-1">
                      {option.description}
                    </div>
                  </div>
                </Button>
              </div>
            ))}
            {filteredDeploymentOptions.length === 0 && searchQuery && (
              <div className="text-sm text-muted-foreground text-center py-4">
                未找到匹配的部署选项
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>

        {/* 环境配置 */}
        <Collapsible open={isEnvironmentConfigOpen} onOpenChange={setIsEnvironmentConfigOpen}>
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              className="w-full justify-between p-0 h-auto font-medium text-left hover:bg-transparent"
            >
              <span className="text-md font-medium">配置环境</span>
              {isEnvironmentConfigOpen ? (
                <ChevronDownIcon className="h-4 w-4" />
              ) : (
                <ChevronRightIcon className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-2 mt-3">
            {filteredEnvironmentConfigs.map((config) => (
              <div key={config.id} className="space-y-1">
                <Button
                  variant={selectedOption === config.id ? 'default' : 'outline'}
                  className="w-full justify-start h-auto p-3"
                  onClick={() => handleOptionSelect(config.id)}
                >
                  <div className="text-left">
                    <div className="font-medium">{config.label}</div>
                    <div className="text-xs text-muted-foreground mt-1">
                      {config.description}
                    </div>
                  </div>
                </Button>
              </div>
            ))}
            {filteredEnvironmentConfigs.length === 0 && searchQuery && (
              <div className="text-sm text-muted-foreground text-center py-4">
                未找到匹配的环境配置
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  );
}
