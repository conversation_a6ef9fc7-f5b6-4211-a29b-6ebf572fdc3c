import React from 'react'
import {
  IconLetterH,
  IconCode,
  IconDatabase,
  IconBrandChrome,
  IconBrandGithub,
  IconBrandDocker,
  IconBrandOpenai,
  IconTools,
  IconJson,
  IconRobot,
  IconMessageChatbot,
  IconMail,
} from '@tabler/icons-react'

// 图标映射
const iconMap: Record<string, React.ReactNode> = {
  '1': <IconLetterH />,
  '2': <IconCode />,
  '3': <IconDatabase />,
  '4': <IconBrandChrome />,
  '5': <IconTools />,
  '6': <IconJson />,
  '7': <IconMessageChatbot />,
  '8': <IconMail />,
  '9': <IconRobot />,
  '10': <IconBrandDocker />,
  '11': <IconBrandGithub />,
  '12': <IconBrandOpenai />,
}

// 图标颜色映射
const iconColorMap: Record<string, string> = {
  '1': 'text-blue-500',
  '2': 'text-green-500',
  '3': 'text-orange-500',
  '4': 'text-blue-400',
  '5': 'text-gray-800 dark:text-gray-200',
  '6': 'text-orange-600',
  '7': 'text-blue-600',
  '8': 'text-red-500',
  '9': 'text-blue-500',
  '10': 'text-blue-600',
  '11': 'text-gray-800 dark:text-gray-200',
  '12': 'text-green-500',
}

/**
 * 根据ID获取图标
 * @param id 图标ID
 * @returns React节点
 */
export const getIconById = (id: string): React.ReactNode => {
  return iconMap[id] || <IconCode />
}

/**
 * 根据ID获取图标颜色
 * @param id 图标ID
 * @returns 颜色类名
 */
export const getIconColor = (id: string): string => {
  return iconColorMap[id] || 'text-gray-500'
}
