import { create } from 'zustand'
import { z } from 'zod'
import { appSchema } from '@/features/apps/data/schema'

type App = z.infer<typeof appSchema>

interface AppsState {
  apps: App[]
  setApps: (apps: App[]) => void
  addApp: (app: App) => void
  updateApp: (id: string, app: Partial<App>) => void
  removeApp: (id: string) => void
}

export const useAppsStore = create<AppsState>()((set) => ({
  apps: [],
  setApps: (apps) => set({ apps }),
  addApp: (app) => set((state) => ({ apps: [...state.apps, app] })),
  updateApp: (id, app) =>
    set((state) => ({
      apps: state.apps.map((a) => (a.id === id ? { ...a, ...app } : a)),
    })),
  removeApp: (id) =>
    set((state) => ({
      apps: state.apps.filter((app) => app.id !== id),
    })),
})) 