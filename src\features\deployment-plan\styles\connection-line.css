/* 自定义连接线样式 */

/* 增强连接线的可见性 */
.react-flow__connection {
  z-index: 1000 !important;
}

/* 自定义连接线样式 - 拖动过程中的连接线 */
.react-flow__connectionline .react-flow__connection-path {
  stroke-width: 2.5 !important;
  stroke: rgba(0, 0, 0, 0.7) !important; /* 黑色带透明度 */
  opacity: 0.8 !important;
  /* 不强制设置虚线和动画，允许通过 connectionLineStyle 自定义 */
}

/* 确保连接线在拖动时保持可见 */
.react-flow.dragging .react-flow__connection-path {
  opacity: 1 !important;
  stroke-width: 3 !important;
  stroke: rgba(0, 0, 0, 0.7) !important; /* 黑色带透明度 */
  /* 不强制设置虚线和动画，允许通过 connectionLineStyle 自定义 */
}

/* 连接线动画 */
@keyframes dash {
  to {
    stroke-dashoffset: -10;
  }
}

/* 所有已连接的线条的基本样式 */
.react-flow__edge .react-flow__edge-path {
  stroke: rgb(114, 111, 111) !important;
  stroke-width: 2 !important;
  /* 不强制设置动画效果，允许自定义 */
}

/* 确保连接点在拖动时保持可见 */
.react-flow.dragging .react-flow__handle {
  transform: scale(1.5) !important;
  opacity: 1 !important;
  box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.3) !important;
  z-index: 1000 !important;
}

/* 确保连接点显示正确的光标 */
.react-flow__handle {
  cursor: crosshair !important;
}
