import { useMutation, useQuery } from '@tanstack/react-query'
import { getProjectList, projectApplication } from '@/api/projects-api.ts'



export function useGetProjectList({currentPage, search}:  {currentPage: number, search: string}) {
  return useQuery({
    queryKey: ['project',currentPage,search],
    queryFn: () => getProjectList(currentPage,search).then(res => res.data.data),
    staleTime: 1000 * 60 * 5, // 5分钟内不重新请求
    // placeholderData: keepPreviousData,
    refetchOnMount: true
  })
}

export function useProjectApplication() {
  return useMutation({
    mutationFn: (projectId:{projectId:number}) => projectApplication(projectId).then(res => res.data),
  })
}