import { useMutation, useQuery } from '@tanstack/react-query'
import axios from '@/lib/axios'
import { DeploymentPlanData } from '@/features/deployment-plan/types'
import { App } from '@/features/apps/data/schema'

/**
 * 获取部署方案列表
 */
export function useDeploymentPlans() {
  return useQuery({
    queryKey: ['deployment-plans'],
    queryFn: () => axios.get<string[]>('/deployment-plans').then(res => res.data),
  })
}

/**
 * 获取特定部署方案
 */
export function useDeploymentPlan(id: string = 'default') {
  return useQuery({
    queryKey: ['deployment-plan', id],
    queryFn: async () => {
      try {
        const response = await axios.get<DeploymentPlanData>(`/deployment-plans/${id}`)
        return response.data
      } catch (error) {
        return { nodes: [], edges: [] }
      }
    },
    retry: false, // 不重试，避免多次失败请求
    refetchOnWindowFocus: false, // 窗口聚焦时不重新请求
  })
}

/**
 * 保存部署方案
 */
export function useSaveDeploymentPlan() {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: DeploymentPlanData }) =>
      axios.post(`/deployment-plans/${id}`, data).then(res => res.data),
  })
}

/**
 * 获取可用于部署方案的应用列表
 */
export function useDeploymentPlanApps() {
  return useQuery({
    queryKey: ['deployment-plan-apps'],
    queryFn: async () => {
      try {
        const response = await axios.get<App[]>('/deployment-plans/apps')
        return response.data
      } catch (error) {
        // 返回一些默认应用数据，以便在后端不可用时仍能显示一些内容
        return [
          {
            id: '1',
            name: 'Headscale',
            desc: '一个自托管的开源 Tailscale 控制服务器实现。',
            logo: '2',
            tag: '工具',
            integrated: false
          },
          {
            id: '2',
            name: 'PostgreSQL',
            desc: '强大的开源关系型数据库系统。',
            logo: '3',
            tag: '数据库',
            integrated: false
          },
          {
            id: '3',
            name: 'Halo',
            desc: '强大易用的开源建站工具。',
            logo: '1',
            tag: '博客',
            integrated: false
          }
        ]
      }
    },
    // 确保返回空数组而不是 undefined
    select: (data) => data || [],
    // 设置缓存策略
    staleTime: 1000 * 60, // 1分钟内不重新请求
    refetchOnMount: true,
    refetchOnWindowFocus: false, // 窗口聚焦时不重新请求，避免频繁出现404错误
    retry: false, // 不重试，避免多次失败请求
  })
}
