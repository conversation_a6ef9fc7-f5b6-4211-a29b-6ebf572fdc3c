export function jsonStart(content: string) {
  return content.includes("```json:smartSelect")
}

export function jsonEnd(content: string) {
  return content.match(/[\s\S]*?```json:smartSelect[\s\S]*?```[\s\S]*/);
}

export function jsonStartSplit(content: string) {
  const match = content.match(
    /([\s\S]*?)```json:smartSelect[\s\S]*?/,
  );
  return match ? match[1] : "";
}

export function splitJson(content: string) {
  const match = content.match(
    /([\s\S]*?)```json:smartSelect([\s\S]*?)```([\s\S]*)/,
  );

  if (match && match.length === 4) {
    return {
      commonMessage: match[1]+match[3],
      smartSelectList: JSON.parse(match[2]),
    };
  }

  return {
    commonMessage: "",
    smartSelectList: []
  };
}