import { FlowiseOptions, FlowiseAgentApi } from '@/services/ai.ts'
import { BaseUrl } from '@/constant.ts'
import { FlowiseClient } from 'flowise-sdk'

export class SmartSelectionAgentApi implements FlowiseAgentApi {
  private readonly BATCH_SIZE = 3; // 每批处理的字符数
  private buffer = '';
  private isUpdating = false;
  private isFinished = false;

  async chat(options: FlowiseOptions) {
    try {
      const client = new FlowiseClient({
        baseUrl: BaseUrl.Flowise.toString(),
        apiKey: import.meta.env.VITE_SMARTSELECTION_FLOWISE_API_KEY,
      })
      const prediction = await client.createPrediction({
        chatflowId: import.meta.env.VITE_SMARTSELECTION_FLOWISE_AGENT_ID,
        question: options.question,
        chatId: options.chatId,
        overrideConfig: options.overrideConfig ?? {},
        streaming: true,
      })

      let accumulatedMessage = ''

      const updateContent = () => {
        if (this.buffer.length > 0 && !this.isFinished) {
          accumulatedMessage += this.buffer
          options.onUpdate?.(accumulatedMessage)
          this.buffer = ''
        }
        this.isUpdating = false
      }

      const scheduleUpdate = () => {
        if (!this.isUpdating && !this.isFinished) {
          this.isUpdating = true
          requestAnimationFrame(updateContent)
        }
      }

      for await (const chunk of prediction) {
        if (this.isFinished) break;

        const { event, data } = chunk
        if (event === 'end' && data === '[DONE]') {
          this.isFinished = true
          // 确保最后的缓冲内容被输出
          if (this.buffer) {
            accumulatedMessage += this.buffer
            options.onUpdate?.(accumulatedMessage)
            this.buffer = ''
          }
          options.onFinish(accumulatedMessage)
          return
        }
        if (event === 'token' && !this.isFinished) {
          this.buffer += data

          // 当缓冲区达到批处理大小时更新
          if (this.buffer.length >= this.BATCH_SIZE) {
            scheduleUpdate()
          }
        }
      }
    } catch (error) {
      this.isFinished = true
      options.onError?.(error as Error)
    }
  }
}