import { useState, useEffect } from 'react'
import { Node } from '@xyflow/react'
import { IconDeviceFloppy } from '@tabler/icons-react'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { getIconById, getIconColor } from '@/features/apps/utils/icon-utils'
import PanelLayout from './shared/panel-layout'

// 定义节点编辑面板的属性
interface NodeEditPanelProps {
  isOpen: boolean;
  onClose: () => void;
  selectedNode: Node | null;
  onSave: (nodeName: string, nodeDescription: string) => void;
}

export default function NodeEditPanel({ isOpen, onClose, selectedNode, onSave }: NodeEditPanelProps) {
  const [nodeName, setNodeName] = useState('')
  const [nodeDescription, setNodeDescription] = useState('')
  const [originalNodeName, setOriginalNodeName] = useState('')
  const [originalNodeDescription, setOriginalNodeDescription] = useState('')
  const [hasChanges, setHasChanges] = useState(false)

  // 当选中节点变化时更新表单值
  useEffect(() => {
    if (selectedNode) {
      // 使用类型断言确保 data 符合预期的类型
      const nodeData = selectedNode.data as { label?: string; description?: string };
      const name = nodeData.label || '';
      const description = nodeData.description || '';

      setNodeName(name)
      setNodeDescription(description)
      setOriginalNodeName(name)
      setOriginalNodeDescription(description)
      setHasChanges(false)
    }
  }, [selectedNode])

  // 检测内容变化
  useEffect(() => {
    if (selectedNode) {
      setHasChanges(
        nodeName !== originalNodeName ||
        nodeDescription !== originalNodeDescription
      )
    }
  }, [nodeName, nodeDescription, originalNodeName, originalNodeDescription, selectedNode])

  // 处理保存
  const handleSave = () => {
    if (selectedNode) {
      onSave(nodeName, nodeDescription)

      // 更新原始值，重置变化状态
      setOriginalNodeName(nodeName)
      setOriginalNodeDescription(nodeDescription)
      setHasChanges(false)
    }
  }

  // 如果面板未打开或没有选中节点，则不显示
  if (!isOpen || !selectedNode) return null

  const nodeContent = (
    <>
      <div className="flex items-center mb-4">
        {/* 使用类型断言确保 data 符合预期的类型 */}
        {(() => {
          const nodeData = selectedNode.data as { icon?: string; tag?: string };
          return (
            <>
              <div className="flex size-10 items-center justify-center rounded-lg p-2 bg-gray-50 dark:bg-gray-700 flex-shrink-0">
                <div className={`text-2xl ${getIconColor(nodeData.icon || '1')}`}>
                  {getIconById(nodeData.icon || '1')}
                </div>
              </div>
              {nodeData.tag && (
                <div className="ml-3 text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-md inline-block">
                  {nodeData.tag}
                </div>
              )}
            </>
          );
        })()}
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="nodeName">节点名称</Label>
          <Input
            id="nodeName"
            value={nodeName}
            onChange={(e) => setNodeName(e.target.value)}
            placeholder="输入节点名称"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="nodeDescription">节点描述</Label>
          <Textarea
            id="nodeDescription"
            value={nodeDescription}
            onChange={(e) => setNodeDescription(e.target.value)}
            placeholder="输入节点描述（可选）"
            rows={8}
          />
        </div>
      </div>
    </>
  )

  const saveButton = (
    <button
      onClick={handleSave}
      className="h-9 w-64 text-base rounded-md flex items-center justify-center gap-2 transition-all duration-200 ease-out bg-primary text-primary-foreground hover:bg-primary/90"
    >
      <IconDeviceFloppy size={18} className="mr-2" />
      保存节点
    </button>
  )

  return (
    <div id="node-edit-panel" data-has-changes={hasChanges.toString()}>
      <PanelLayout
        title="编辑节点"
        onClose={onClose}
        footer={saveButton}
      >
        {nodeContent}
      </PanelLayout>
    </div>
  )
}
