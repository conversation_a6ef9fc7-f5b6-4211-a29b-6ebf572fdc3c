import { useState, useEffect } from 'react'
import { Node } from '@xyflow/react'
import { IconDeviceFloppy, IconServer, IconCode } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'
import { PanelRightIcon } from 'lucide-react'
import { Separator } from '@/components/ui/separator'
import { getIconById, getIconColor } from '@/features/apps/utils/icon-utils'
import { HostNode } from '../types'

// 定义节点编辑面板的属性
interface NodeEditPanelProps {
  isOpen: boolean;
  onClose: () => void;
  selectedNode: Node | null;
  onSave: (updatedData: any) => void;
}

export default function NodeEditPanel({ isOpen, onClose, selectedNode, onSave }: NodeEditPanelProps) {
  // 通用节点字段
  const [nodeName, setNodeName] = useState('')
  const [nodeDescription, setNodeDescription] = useState('')
  
  // 主机节点特有字段
  const [ipAddress, setIpAddress] = useState('')
  const [operatingSystem, setOperatingSystem] = useState('')
  const [diskSpace, setDiskSpace] = useState('')
  const [memory, setMemory] = useState('')
  const [cpuCores, setCpuCores] = useState('')

  // 原始值用于检测变化
  const [originalValues, setOriginalValues] = useState<any>({})
  const [hasChanges, setHasChanges] = useState(false)

  // 判断是否为主机节点
  const isHostNode = selectedNode?.data?.nodeType === 'host'

  // 当选中节点变化时更新表单值
  useEffect(() => {
    if (selectedNode) {
      const nodeData = selectedNode.data as any;
      
      // 通用字段
      const name = nodeData.label || '';
      const description = nodeData.description || '';
      
      setNodeName(name)
      setNodeDescription(description)

      // 主机节点特有字段
      if (isHostNode) {
        const hostData = nodeData as HostNode['data'];
        setIpAddress(hostData.ipAddress || '')
        setOperatingSystem(hostData.operatingSystem || '')
        setDiskSpace(hostData.diskSpace || '')
        setMemory(hostData.memory || '')
        setCpuCores(hostData.cpuCores || '')
      }

      // 保存原始值
      const original = {
        nodeName: name,
        nodeDescription: description,
        ...(isHostNode && {
          ipAddress: nodeData.ipAddress || '',
          operatingSystem: nodeData.operatingSystem || '',
          diskSpace: nodeData.diskSpace || '',
          memory: nodeData.memory || '',
          cpuCores: nodeData.cpuCores || ''
        })
      }
      setOriginalValues(original)
      setHasChanges(false)
    }
  }, [selectedNode, isHostNode])

  // 检测内容变化
  useEffect(() => {
    if (selectedNode) {
      const currentValues = {
        nodeName,
        nodeDescription,
        ...(isHostNode && {
          ipAddress,
          operatingSystem,
          diskSpace,
          memory,
          cpuCores
        })
      }

      const changed = Object.keys(currentValues).some(
        key => currentValues[key] !== originalValues[key]
      )
      setHasChanges(changed)
    }
  }, [nodeName, nodeDescription, ipAddress, operatingSystem, diskSpace, memory, cpuCores, originalValues, selectedNode, isHostNode])

  // 处理保存
  const handleSave = () => {
    if (selectedNode) {
      if (isHostNode) {
        // 验证主机节点必填字段
        if (!ipAddress.trim()) {
          alert('请输入IP地址');
          return;
        }
        if (!nodeName.trim()) {
          alert('请输入主机名称');
          return;
        }

        onSave({
          label: ipAddress, // 使用IP地址作为节点名称
          description: nodeName, // 使用主机名称作为描述
          ipAddress,
          operatingSystem,
          diskSpace,
          memory,
          cpuCores
        })
      } else {
        // 普通节点保存
        onSave(nodeName, nodeDescription)
      }

      // 更新原始值，重置变化状态
      const newOriginal = {
        nodeName,
        nodeDescription,
        ...(isHostNode && {
          ipAddress,
          operatingSystem,
          diskSpace,
          memory,
          cpuCores
        })
      }
      setOriginalValues(newOriginal)
      setHasChanges(false)
    }
  }

  // 如果面板未打开或没有选中节点，则不显示
  if (!isOpen || !selectedNode) return null

  return (
    <div className="h-full flex flex-col">
      {/* 标题栏 */}
      <div className="flex items-center justify-between mb-6 pb-3 border-b border-b-gray-200 dark:border-b-gray-700">
        <div className="flex items-center">
          <Button
            data-sidebar="trigger"
            data-slot="sidebar-trigger"
            variant="outline"
            size="icon"
            className={cn('size-7', 'scale-125 sm:scale-100', 'mr-3')}
            onClick={onClose}
          >
            <PanelRightIcon />
            <span className="sr-only">Toggle Sidebar</span>
          </Button>
          <Separator orientation="vertical" className="h-4 mr-3" />
          <h3 className="text-lg font-medium">
            {isHostNode ? '编辑主机节点' : '编辑节点'}
          </h3>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-auto">
        {/* 节点图标和标签 */}
        <div className="flex items-center mb-6">
          {(() => {
            const nodeData = selectedNode.data as { icon?: string; tag?: string };
            return (
              <>
                <div className="flex size-12 items-center justify-center rounded-lg p-2 bg-gray-50 dark:bg-gray-700 flex-shrink-0">
                  <div className={`text-3xl ${getIconColor(nodeData.icon || '1')}`}>
                    {getIconById(nodeData.icon || '1')}
                  </div>
                </div>
                {nodeData.tag && (
                  <div className="ml-4 text-sm px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md inline-block">
                    {nodeData.tag}
                  </div>
                )}
                <div className="ml-auto">
                  {isHostNode ? (
                    <IconServer className="text-blue-500" size={20} />
                  ) : (
                    <IconCode className="text-green-500" size={20} />
                  )}
                </div>
              </>
            );
          })()}
        </div>

        {/* 表单字段 */}
        <div className="space-y-6">
          {isHostNode ? (
            <>
              {/* 主机节点字段 */}
              <div className="space-y-2">
                <Label htmlFor="nodeName">主机名称 <span className="text-red-500">*</span></Label>
                <Input
                  id="nodeName"
                  value={nodeName}
                  onChange={(e) => setNodeName(e.target.value)}
                  placeholder="输入主机名称"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="ipAddress">IP地址 <span className="text-red-500">*</span></Label>
                <Input
                  id="ipAddress"
                  value={ipAddress}
                  onChange={(e) => setIpAddress(e.target.value)}
                  placeholder="例如: *************"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="operatingSystem">操作系统</Label>
                <Input
                  id="operatingSystem"
                  value={operatingSystem}
                  onChange={(e) => setOperatingSystem(e.target.value)}
                  placeholder="例如: Windows Server 2022, Ubuntu 22.04"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="cpuCores">CPU核心数</Label>
                  <div className="flex items-center">
                    <Input
                      id="cpuCores"
                      value={cpuCores}
                      onChange={(e) => setCpuCores(e.target.value)}
                      placeholder="4"
                      className="flex-1"
                    />
                    <span className="ml-2 text-sm text-gray-500">核</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="memory">内存容量</Label>
                  <div className="flex items-center">
                    <Input
                      id="memory"
                      value={memory}
                      onChange={(e) => setMemory(e.target.value)}
                      placeholder="16"
                      className="flex-1"
                    />
                    <span className="ml-2 text-sm text-gray-500">GB</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="diskSpace">磁盘容量</Label>
                <div className="flex items-center">
                  <Input
                    id="diskSpace"
                    value={diskSpace}
                    onChange={(e) => setDiskSpace(e.target.value)}
                    placeholder="500"
                    className="flex-1"
                  />
                  <span className="ml-2 text-sm text-gray-500">GB</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="nodeDescription">备注说明</Label>
                <Textarea
                  id="nodeDescription"
                  value={nodeDescription}
                  onChange={(e) => setNodeDescription(e.target.value)}
                  placeholder="输入主机描述（可选）"
                  rows={4}
                />
              </div>
            </>
          ) : (
            <>
              {/* 普通节点字段 */}
              <div className="space-y-2">
                <Label htmlFor="nodeName">节点名称</Label>
                <Input
                  id="nodeName"
                  value={nodeName}
                  onChange={(e) => setNodeName(e.target.value)}
                  placeholder="输入节点名称"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="nodeDescription">节点描述</Label>
                <Textarea
                  id="nodeDescription"
                  value={nodeDescription}
                  onChange={(e) => setNodeDescription(e.target.value)}
                  placeholder="输入节点描述（可选）"
                  rows={8}
                />
              </div>
            </>
          )}
        </div>
      </div>

      {/* 底部保存按钮 */}
      <div className="mt-auto pt-6 border-t border-gray-200 dark:border-gray-700">
        <div className="flex justify-center">
          <Button
            onClick={handleSave}
            disabled={!hasChanges}
            className="h-10 w-64 text-base rounded-md flex items-center justify-center gap-2 transition-all duration-200 ease-out"
          >
            <IconDeviceFloppy size={18} className="mr-2" />
            {isHostNode ? '保存主机' : '保存节点'}
          </Button>
        </div>
      </div>
    </div>
  )
}
