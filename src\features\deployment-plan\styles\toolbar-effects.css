/* 工具栏按钮效果 */
.toolbar-button {
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(var(--primary-rgb), 0.3);
  padding-bottom: 4px;
  margin-bottom: 4px;
}

/* 深色模式下的边框颜色 */
.dark .toolbar-button {
  border-bottom: 1px solid rgba(var(--primary-rgb), 0.4);
}

/* 工具栏图标大小 */
.toolbar-icon {
  height: 1.15rem;
  width: 1.15rem;
}

.toolbar-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(var(--primary-rgb), 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.toolbar-button:active::after {
  opacity: 1;
  width: 100%;
  height: 100%;
  border-radius: 0;
  transform: scale(0, 0) translate(-50%, -50%);
  transition: transform 0.3s, opacity 0.3s;
}

/* 工具栏容器效果 */
.toolbar-container {
  position: relative;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dark .toolbar-container {
  background-color: rgba(30, 30, 30, 0.15);
  border: 1px solid rgba(50, 50, 50, 0.2);
}

/* 分隔线样式 */
.toolbar-separator {
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(var(--primary-rgb), 0.3), transparent);
  margin: 2px 0;
  padding: 0;
  opacity: 0.8;
}
