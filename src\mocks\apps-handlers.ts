import { http, HttpResponse } from 'msw'
import { apps } from '../features/apps/data/apps'

export const appsHandlers = [
  // Apps数据mock
  http.get('/api/apps/getApps', async () => {
    await new Promise(resolve => setTimeout(resolve, 500))

    // 将 React 元素转换为字符串标识符
    const serializedApps = apps.map(app => ({
      ...app,
      // 将 logo 替换为图标名称字符串
      logo: app.id // 使用 id 作为图标标识符
    }))

    return HttpResponse.json(serializedApps)
  }),

  // 更新App集成状态
  http.post('/api/apps/updateConnection', async ({request}) => {
    await new Promise(resolve => setTimeout(resolve, 800))
    const body = await request.json() as { id: string; integrated: boolean }

    // 在实际应用中，这里会更新数据库中的数据
    // 在mock中，我们找到对应的app并更新其集成状态
    const app = apps.find(app => app.id === body.id)

    if (app) {
      // 创建一个新对象，避免直接修改原始数据
      const updatedApp = {
        ...app,
        integrated: body.integrated,
        // 将 logo 替换为图标名称字符串
        logo: app.id // 使用 id 作为图标标识符
      }

      return HttpResponse.json(updatedApp)
    }

    // 如果找不到对应的app，返回错误
    return HttpResponse.json(
      { error: 'App not found', code: 404 },
      { status: 404 }
    )
  })
]
