import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import AuthLayout from '../auth-layout'
import { EmailAuthForm, UserAuthForm } from './components/user-auth-form'
import { useEffect, useState } from 'react'
import { useSearch } from '@tanstack/react-router'
import { Fragment } from 'react/jsx-runtime'


export default function SignIn() {
  const [type, setType] = useState<'common' | 'email'>('common')
  const search = useSearch({ from: '/(auth)/sign-in' }) as { type: string, token: string }
  const [send, setSend] = useState(false)
  const [email, setEmail] = useState('')

  useEffect(() => {
    if (search.type === 'email') {
      setType('email')
    } else {
      setType('common')
    }
  }, [search.type])


  return (
    <AuthLayout>
      <Card className="gap-4">
        {type === 'common' && <Fragment>
          <CardHeader>
            <CardTitle className="text-lg tracking-tight">Login</CardTitle>
            <CardDescription>
              Enter your email and password below to <br />
              log into your account
            </CardDescription>
          </CardHeader>
          <CardContent>
            <UserAuthForm />
          </CardContent>
          <CardFooter />
        </Fragment>}
        {type === 'email' && !send &&
          <Fragment>
            <CardHeader>
              <CardTitle className="text-lg tracking-tight">公司邮箱登录</CardTitle>
              <CardDescription>
                系统将会发送登录链接到您的公司邮箱, 点击登录链接完成登录。
              </CardDescription>
            </CardHeader>
            <CardContent>
              <EmailAuthForm onSuccess={(email) => {
                setSend(true)
                setEmail(email)
              }} />
            </CardContent>
            <CardFooter>
              <div className="mt-10" />
            </CardFooter>
          </Fragment>
        }
        {type === 'email' && send &&
          <Fragment>
            <CardHeader>
              <CardTitle className="text-3xl tracking-tight pb-4 pt-5">邮件已发送</CardTitle>
              <CardDescription>
                <span className="text-black">我们已经将登录链接发送到您的公司邮箱：</span>
                <span className="text-black font-bold">{email}</span>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <hr />
            </CardContent>
            <CardFooter>
              <div className="flex text-sm text-gray-500" >
                再次<a href="/sign-in?type=email" className="text-blue-500">发送链接</a>，或者在<a
                href="/help-center" className="text-blue-500">帮助中心</a>查找更多信息。
              </div>

            </CardFooter>
          </Fragment>
        }
      </Card>
    </AuthLayout>
  )
}
