/* 自定义光标样式 */

/* 默认箭头光标 - 使用更精致的箭头 */
.select-mode,
.select-mode .react-flow__pane,
.select-mode .react-flow__renderer {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="white" stroke="%23000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M6 3l12 9-5 2-1 5-3-7z"/></svg>') 0 0, auto !important;
}

/* 拖动模式光标 - 使用更简洁的手形图标 */
.pan-mode,
.pan-mode .react-flow__pane,
.pan-mode .react-flow__renderer {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%23000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M8 13v-2.5a1.5 1.5 0 0 1 3 0V12"/><path d="M11 11.5v-2a1.5 1.5 0 1 1 3 0V12"/><path d="M14 10.5v-1a1.5 1.5 0 0 1 3 0V12"/><path d="M17 11.5a1.5 1.5 0 0 1 3 0V16a6 6 0 0 1-6 6h-2 .208a6 6 0 0 1-5.012-2.7L7 19c-.312-.479-1.407-2.388-3.286-5.728a1.5 1.5 0 0 1 .536-2.022 1.867 1.867 0 0 1 2.28.28L8 13"/></svg>') 5 0, grab !important;
}

/* 拖动中的光标 - 使用更简洁的抓取图标 */
.pan-mode:active,
.pan-mode:active .react-flow__pane,
.pan-mode:active .react-flow__renderer {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%23000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M9 5.5V10"/><path d="M13 5.5V10"/><path d="M17 5.5V10"/><path d="M12.9 20.12c-1.74.12-3.4-.82-4.1-2.12l-4.76-8.73A1.5 1.5 0 0 1 5.11 7.2c.46-.4 1.76-.3 2.29.5L9 10"/><path d="M12.96 20.15c2.09 0 3.94-1.36 4.54-3.35l2.78-9.14a1.5 1.5 0 0 0-1.47-1.66c-.7 0-1.28.5-1.44 1.17L16 10"/></svg>') 5 0, grabbing !important;
}

/* 节点悬停时的移动光标 - 使用四向箭头 */
.react-flow__node:hover {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M18 9l3 3l-3 3" /><path d="M15 12h6" /><path d="M6 9l-3 3l3 3" /><path d="M3 12h6" /><path d="M9 18l3 3l3 -3" /><path d="M12 15v6" /><path d="M15 6l-3 -3l-3 3" /><path d="M12 3v6" /></svg>') 12 12, move !important;
}

/* 连接点悬停时的指针光标 */
.react-flow__handle:hover {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="white" stroke="%23000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M8 13v-8.5a1.5 1.5 0 0 1 3 0V12"/><path d="M11 11.5v-7a1.5 1.5 0 0 1 3 0V12"/><path d="M14 10.5v-5a1.5 1.5 0 0 1 3 0V12"/><path d="M17 11.5a1.5 1.5 0 0 1 3 0V16a6 6 0 0 1-6 6h-2 .208a6 6 0 0 1-5.012-2.7A69.74 69.74 0 0 1 7 19c-.312-.479-1.407-2.388-3.286-5.728a1.5 1.5 0 0 1 .536-2.022 1.867 1.867 0 0 1 2.28.28L8 13"/></svg>') 5 0, pointer !important;
}

/* 连接拖动时的光标 - 使用黑色十字光标 */
.react-flow.dragging,
.react-flow.dragging .react-flow__pane,
.react-flow.dragging .react-flow__renderer,
.react-flow.dragging .react-flow__node:hover {
  cursor: crosshair !important;
}

/* 确保连接拖动时节点不会改变光标 */
.react-flow.dragging .react-flow__node:hover {
  cursor: crosshair !important;
}

/* 备用光标样式 - 如果自定义光标不起作用 */
.select-mode {
  cursor: default !important;
}

.pan-mode {
  cursor: grab !important;
}

.pan-mode:active {
  cursor: grabbing !important;
}

/* 连接拖动时的备用光标 */
.react-flow.dragging {
  cursor: crosshair !important;
}

/* 确保连接点在拖动时保持可见 */
.react-flow.dragging .react-flow__handle {
  opacity: 1 !important;
}

/* 禁用圆圈光标 */
.circle-off-cursor {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%23000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5.63 5.64a9 9 0 1 0 12.73 12.73m1.64 -2.28a9 9 0 0 0 -12.1 -12.1"></path><path d="M22 22l-20 -20"></path></svg>') 12 12, not-allowed !important;
}

