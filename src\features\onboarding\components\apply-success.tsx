import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card.tsx'

export default function ApplySuccess({ projectName }: { projectName: string }) {

  return (
    <Card className="gap-4 w-full max-w-xl">
      <CardHeader>
        <CardTitle className="text-3xl tracking-tight pb-4 pt-5">项目申请成功</CardTitle>
        <CardDescription>
          <span className="text-black">您已成功申请加入</span>
          <span className="text-black font-bold">{projectName}</span>
          <span className="text-black">，请等待项目管理员的审批。</span>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <hr />
      </CardContent>
      <CardFooter>
        <div className="flex text-sm text-gray-500">
          请<a href="/onboarding" className="text-blue-500">返回</a>，或者在<a
          href="/help-center" className="text-blue-500">帮助中心</a>查找更多信息。
        </div>
      </CardFooter>
    </Card>
  )
}