/* 连接线样式定义 */

/* 基本边样式 - 覆盖所有其他样式 */
.react-flow__edge .react-flow__edge-path {
  stroke: rgba(0, 0, 0, 0.7) !important; /* 黑色带透明度 */
  stroke-width: 2 !important;
}

/* 虚线样式 */
.react-flow__edge[class*="dashed"] .react-flow__edge-path,
.react-flow__edge[class*="dashed-arrow"] .react-flow__edge-path {
  stroke-dasharray: 5, 3 !important;
}

/* 实线样式 */
.react-flow__edge[class*="solid"] .react-flow__edge-path {
  stroke-dasharray: none !important;
}

/* 动画效果 - 只应用于虚线 */
.react-flow__edge[class*="dashed"] .react-flow__edge-path {
  animation: dash 0.5s linear infinite !important;
}

/* 无动画效果 - 应用于实线 */
.react-flow__edge[class*="solid"] .react-flow__edge-path {
  animation: none !important;
}

/* 连接线动画 */
@keyframes dash {
  to {
    stroke-dashoffset: -10;
  }
}

/* 确保箭头标记可见 */
.react-flow__edge .react-flow__edge-path {
  marker-end: inherit !important;
}

/* 选中边的样式 */
.react-flow__edge.selected .react-flow__edge-path {
  stroke-width: 2.5 !important;
  stroke: var(--primary) !important;
  filter: drop-shadow(0 0 2px rgba(var(--primary-rgb), 0.3));
}

/* 特定样式类 */
.react-flow__edge.dashed .react-flow__edge-path {
  stroke-dasharray: 5, 3 !important;
  animation: dash 0.5s linear infinite !important;
}

.react-flow__edge.dashed-arrow-open .react-flow__edge-path {
  stroke-dasharray: 5, 3 !important;
  animation: dash 0.5s linear infinite !important;
  marker-end: url(#arrow-open) !important;
}

.react-flow__edge.solid-arrow-open .react-flow__edge-path {
  stroke-dasharray: none !important;
  animation: none !important;
  marker-end: url(#arrow-open) !important;
}

.react-flow__edge.dashed-arrow-filled .react-flow__edge-path {
  stroke-dasharray: 5, 3 !important;
  animation: dash 0.5s linear infinite !important;
  marker-end: url(#arrow-filled) !important;
}

.react-flow__edge.solid-arrow-filled .react-flow__edge-path {
  stroke-dasharray: none !important;
  animation: none !important;
  marker-end: url(#arrow-filled) !important;
}
