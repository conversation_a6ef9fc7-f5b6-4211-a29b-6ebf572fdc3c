import { useEffect, useState } from 'react'
import { Project } from '@/features/onboarding/data/schema.ts'
import { useGetProjectList, useProjectApplication } from '@/hooks/project-query.tsx'
import { Button } from '@/components/ui/button.tsx'
import { ChevronLeftIcon, ChevronRightIcon, DoubleArrowLeftIcon, DoubleArrowRightIcon } from '@radix-ui/react-icons'
import {
  Dialog, DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { toast } from 'sonner'
import { Loading } from '@/components/loading.tsx'
import { Loader2 } from 'lucide-react'
import { Fragment } from 'react/jsx-runtime'

export default function ProjectApplication({ onApply }: { onApply: (projectName: string) => void }) {
  const [search, setSearch] = useState('')
  const [inputValue, setInputValue] = useState('')
  const [loading, setLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPage, setTotalPage] = useState(1)
  const [projectList, setProjectList] = useState<Project[]>([])
  const { data, isLoading, isError } = useGetProjectList({ currentPage, search })
  const projectApplication = useProjectApplication()

  useEffect(() => {
    if (data) {
      setTotalPage(data.totalPage)
      setProjectList(data.list)
    }
  }, [data])

  function projectApply(projectId: number, projectName: string) {
    setLoading(true)
    projectApplication.mutateAsync({ projectId: projectId }).then((res) => {
      if (res.code === 200) {
        onApply(projectName)
      }
    }).catch((err) => {
      toast.error(err.message)
    }).finally(() => {
      setLoading(false)
    })
  }

  return (
    <Fragment>
      <h1 className="text-3xl font-bold mb-5">申请加入项目</h1>
      <div className="w-full max-w-xl mb-5">
        <div className="relative">
          <input
            type="text"
            className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-400"
            placeholder="请输入关键字"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                setCurrentPage(1)
                setSearch(inputValue)
              }
            }}
          />
          <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              <svg width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <circle cx="11" cy="11" r="8" />
                <line x1="21" y1="21" x2="16.65" y2="16.65" />
              </svg>
            </span>
        </div>
      </div>
      <div className="w-full max-w-xl">
        {isLoading &&
          <div className="flex justify-center items-center py-20">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            数据加载中...
          </div>
        }
        {!isLoading && !isError && projectList.length > 0 && projectList.map((item) => (
          <div key={item.id} className="flex items-center bg-white p-2">
            <div className="w-12 h-12 bg-gray-200 rounded-full mr-6 flex items-center justify-center">
              <div>{item.name.charAt(0)}</div>
            </div>
            <div className="flex-1 mr-32">
              <div className="text-lg font-bold">{item.name}</div>
              <div className="text-gray-500 mt-1 text-sm">管理员：{item.admin}</div>
            </div>
            <Dialog>
              <DialogTrigger asChild>
                <Button
                  className="ml-8 px-6 py-2 bg-sky-500 text-white rounded hover:bg-sky-600 transition"
                >申请
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>确认</DialogTitle>
                  <DialogDescription>
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <span>申请加入{item.name}?</span>
                </div>
                <DialogFooter>
                  <DialogClose asChild>
                    <Button type="submit" onClick={() => projectApply(item.id, item.name)}>确认</Button>
                  </DialogClose>
                  <DialogClose asChild>
                    <Button type="button" variant="secondary">
                      取消
                    </Button>
                  </DialogClose>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        ))}
        {!isLoading && !isError && projectList.length === 0 &&
          <div className="text-center text-gray-500">暂无项目</div>}
      </div>
      <div
        className="flex items-center justify-between overflow-clip mt-5"
        style={{ overflowClipMargin: 1 }}
      >
        <div className="flex items-center sm:space-x-6 lg:space-x-8">
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            第{currentPage}页/共{totalPage}页
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => setCurrentPage(1)}
              disabled={currentPage === 1}
            >
              <span className="sr-only">Go to first page</span>
              <DoubleArrowLeftIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => setCurrentPage((pre) => pre - 1)}
              disabled={currentPage === 1}
            >
              <span className="sr-only">Go to previous page</span>
              <ChevronLeftIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => setCurrentPage((pre) => pre + 1)}
              disabled={currentPage === totalPage}
            >
              <span className="sr-only">Go to next page</span>
              <ChevronRightIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => setCurrentPage(totalPage)}
              disabled={currentPage === totalPage}
            >
              <span className="sr-only">Go to last page</span>
              <DoubleArrowRightIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
      {loading && <Loading text={'申请中,请等待...'} />}
    </Fragment>
  )
}