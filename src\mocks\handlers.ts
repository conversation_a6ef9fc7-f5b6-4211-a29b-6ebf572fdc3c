import { http, HttpResponse } from 'msw'

const users = [
  {
    id: 1,
    name: 'aisecops',
    email: '<EMAIL>',
    password: '1234567',
    role: ['admin'],
    avatar: '',
    access_token: '6KFbDjuK53CQGqWskKfkcsRCKM7Q0Ex5vmAVckwLPGKXM0ykQaU1dU5QiNevZ',
    projects: [],
  },
  {
    id: 2,
    name: 'monster',
    email: '<EMAIL>',
    password: '1234567',
    role: ['user'],
    avatar: '',
    access_token: '2KEeDjuK53CQGqWskKfkcsRCKM7Q0Ex5vmAVckwLPGKXM0ykQaU1dU5QiUvws',
    projects: [1,2],
  },
]

const projects = [
  {
    id: 1,
    name: '智博项目',
    admin: 'admin',
    description: '智博AI',
  },
  {
    id: 2,
    name: '鹰智达项目',
    admin: 'admin',
    logo: 'Fa<PERSON>',
    description: '鹰智达',
  },
  {
    id: 3,
    name: '快码项目',
    admin: 'admin',
    logo: 'FaP',
    description: '快码开发框架',
  },
  {
    id: 4,
    name: '智慧运维项目',
    admin: 'admin',
    logo: 'FaP',
    description: '智慧运维体系',
  },
  {
    id: 5,
    name: '驯码项目',
    admin: 'admin',
    logo: 'FaP',
    description: '驯码学习平台',
  },
  {
    id: 6,
    name: '查码先锋项目',
    admin: 'admin',
    logo: 'FaP',
    description: '查码先锋',
  },
]

//验证token
function verifyToken(request: Request) {
  const token = request.headers.get('Authorization')
  if (!token) {
    return null
  }
  const accessToken = token.split(' ')[1]
  return users.find(user => user.access_token === accessToken)
}

export const userHandlers = [
  // 登录接口mock
  http.post('/api/auth/login', async ({ request }) => {
    await new Promise(resolve => setTimeout(resolve, 1000))
    const body = await request.json() as { email: string; password: string }
    if (!body.email || !body.password) {
      return HttpResponse.json({
        error: '账号或密码不能为空',
        code: 400,
      })
    }
    const user = users.find(user => user.email === body.email)
    if (!user) {
      return HttpResponse.json({
        error: '用户不存在',
        code: 400,
      })
    }
    if (user.password !== body.password) {
      return HttpResponse.json({
        error: '密码错误',
        code: 400,
      })
    }
    user.password = ''
    return HttpResponse.json({
      accessToken: user.access_token,
      user: user,
      code: 200,
    })
  }),

  http.get('/api/user/getCurrentUser', async ({ request }) => {
    const user = verifyToken(request)
    if (!user) {
      return HttpResponse.json({
        error: '未登录',
        code: 401,
      }, {
        status: 401,
      })
    }
    user.password = ''
    return HttpResponse.json({
      user: user,
      code: 200,
    })
  }),

  // 用户数据mock
  http.get('/api/user/getUserProjects', async ({ request }) => {
    await new Promise(resolve => setTimeout(resolve, 500))
    const user = verifyToken(request)
    if (!user) {
      return HttpResponse.json({
        error: '未登录',
        code: 401,
      }, {
        status: 401,
      })
    }
    const list = projects.filter(project => user.projects.includes(project.id))
    return HttpResponse.json({
      code: 200,
      list: list,
    })
  }),
]



export const projectHandlers = [
  http.get('/api/project/list', async ({ request }) => {
    const user = verifyToken(request)
    if (!user) {
      return HttpResponse.json({
        error: '未登录',
        code: 401,
      }, {
        status: 401,
      })
    }
    // 解析URL获取查询参数
    const url = new URL(request.url)
    const currentPage = url.searchParams.get('page')
    const search = url.searchParams.get('search')
    const start = (Number(currentPage) - 1) * 4
    const end = start + 4
    let filteredProjects = projects
    if (search && search.length > 0) {
      filteredProjects = filteredProjects.filter(project => project.name.includes(search))
    }
    let totalPage = Math.ceil(filteredProjects.length / 4)
    if (totalPage === 0) {
      totalPage = 1
    }

    const paginatedProjects = filteredProjects.slice(start, end)

    return HttpResponse.json({
      code: 200,
      data: {
        currentPage: Number(currentPage),
        totalPage: totalPage,
        list: paginatedProjects,
      },
    })

  }),

  http.post('/api/project/apply', async ({ request }) => {
    await new Promise(resolve => setTimeout(resolve, 2000))
    const user = verifyToken(request)
    if (!user) {
      return HttpResponse.json({
        error: '未登录',
        code: 401,
      }, {
        status: 401,
      })
    }
    return HttpResponse.json({
      code: 200,
      message: '申请成功',
    })
  }),
]