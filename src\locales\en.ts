const en = {
  login:{
    success: "Login success",
  },
  Sidebar: {
    general: "General",
    dashboard: "Dashboard",
    tasks: "Tasks",
    app: "Apps",
    deploymentPlan: "Deployment Plan",
    chat: "Chats",
    ai: "AI",
    user: "Users",
    other: "Other",
    settings: "Settings",
    profile: "Profile",
    account: "Account",
    appearance: "Appearance",
    notifications: "Notifications",
    display: "Display",
    helpCenter: "Help Center"
  },
  ai:{
    history: "History Messages",
    newChat: "New Chat",
    placeholder: "Send a message to AISecOps (Use Ctrl+Enter for line break and Enter to send)",
    send: "Send",
    greeting: "Hello",
    helpQuestion: "How can I help you?"
  },
  apps: {
    title: "App Integrations",
    filter: "Filter apps...",
    all: "All Apps",
    deployed: "Integrated",
    notDeployed: "Not Integrated",
    ascending: "Ascending",
    descending: "Descending",
    deploy: "Integrate",
    deployed_status: "Integrated",
    error: "Failed to load apps. Please try again later.",
    tool: "Tool",
    blog: "Blog",
    ai: "AI",
    smartSelection: "Smart Selection",
    loading: "Loading..."
  },
  deploymentPlan: {
    title: "Deployment Plan",
    addApp: "Add App",
    editNode: "Edit Node",
    saveNode: "Save",
    cancel: "Cancel",
    nodeName: "Node Name",
    nodeDescription: "Node Description",
    addToCanvas: "Add to Canvas",
    noApps: "No apps available. Please add apps first.",
    connectionError: "Connection failed, please try again",
    connectionExists: "Connection already exists",
    connectionSuccess: "Connection created successfully",
    connectionTip: "Select a node, then hold Alt and click another node to create a connection",
    savePlan: "Save Plan",
    planSaved: "Deployment plan saved",
    planSaveFailed: "Failed to save deployment plan",
    appAdded: "App added to canvas",
    createNode: "Create Node",
    nodeCreated: "Node created",
    nodeCreateTip: "Hold Shift key and click on canvas to create a new node",
    tips: "Tips",
    tipShiftClick: "Hold Shift and click to create node",
    tipClickNode: "Click node to edit",
    tipDeleteNode: "Press Delete key to remove selected node",
    tipDragConnect: "Drag from node handles to create connections",
    nodeTag: "Tag",
    nodeIcon: "Icon",
    addToDeploymentPlan: "Add to Deployment Plan",
    integrate: "Integrate",
    addedToDeploymentPlan: "Added to Deployment Plan",
    viewDeploymentPlan: "view it now?",
    addToDeploymentPlanFailed: "Failed to add to deployment plan"
  }
};

export type LocaleType = typeof en;
export default en;



