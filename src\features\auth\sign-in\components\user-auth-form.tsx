import { HTMLAttributes, useState } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Link, useNavigate } from '@tanstack/react-router'
import { IconLink } from '@tabler/icons-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { PasswordInput } from '@/components/password-input'
import { Loader2 } from 'lucide-react'
import { userLogin } from '@/api/users-api.ts'
import { toast } from 'sonner'
import { useLanguage } from '@/context/language-context.tsx'
import { useAuthStore } from '@/stores/authStore.ts'

type UserAuthFormProps = HTMLAttributes<HTMLFormElement>
type EmailAuthFormProps = HTMLAttributes<HTMLFormElement> & {
  onSuccess: (email:string) => void
}

const formSchema = z.object({
  email: z
    .string()
    .min(1, { message: 'Please enter your email' })
    .email({ message: 'Invalid email address' }),
  password: z
    .string()
    .min(1, {
      message: 'Please enter your password',
    })
    .min(7, {
      message: 'Password must be at least 7 characters long',
    }),
})

const emailFormSchema = z.object({
  email: z
    .string()
    .min(1, { message: 'Please enter your email' })
    .email({ message: 'Invalid email address' }),
})


export function UserAuthForm({ className, ...props }: UserAuthFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { auth } = useAuthStore()
  const navigate = useNavigate()
  const { t } = useLanguage()
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '<EMAIL>',
      password: '',
    },
  })

  function onSubmit(data: z.infer<typeof formSchema>) {
    setIsLoading(true)
    userLogin(data).then((res) => {
      if (res.data.code === 200) {
        toast.success(t.login.success)
        auth.setAccessToken(res.data.accessToken)
        //用户没有加入任何项目
        if(res.data.user.projects.length === 0){
          navigate({
            to: '/onboarding',
          })
          return
        }
        // 检查是否存在 redirect 参数
        const searchParams = new URLSearchParams(window.location.search)
        const redirect = searchParams.get('redirect')

        // 跳转到 redirect 或默认的 /dashboard
        navigate({ to: redirect || '/dashboard' })
      } else {
        toast.error(res.data.error)
      }
    }).finally(() => {
      setIsLoading(false)
    })
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn('grid gap-3', className)}
        {...props}
      >
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem className="relative">
              <FormLabel>Password</FormLabel>
              <FormControl>
                <PasswordInput placeholder="********" {...field} />
              </FormControl>
              <FormMessage />
              <Link
                to="/forgot-password"
                className="text-muted-foreground absolute -top-0.5 right-0 text-sm font-medium hover:opacity-75"
              >
                Forgot password?
              </Link>
            </FormItem>
          )}
        />
        <Button className="mt-2" disabled={isLoading}>
          {isLoading ? (
            <div className="flex items-center gap-2">
              <Loader2 className="animate-spin" />
              <span>Please wait</span>
            </div>
          ) : (
            'Login'
          )}
        </Button>

        <div className="relative my-2">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background text-muted-foreground px-2">
              Or continue with
            </span>
          </div>
        </div>

        <div className="grid">
          <Button variant="outline" type="button" disabled={isLoading} onClick={() => navigate(
            {
              to: '/sign-in',
              search: {
                type: 'email',
              },
            })}>
            <IconLink /> 公司邮箱登录
          </Button>
        </div>
      </form>
    </Form>
  )
}

export function EmailAuthForm({ className,onSuccess, ...props }: EmailAuthFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  // const { t } = useLanguage()
  const form = useForm<z.infer<typeof emailFormSchema>>({
    resolver: zodResolver(emailFormSchema),
    defaultValues: {
      email: '<EMAIL>',
    },
  })

  function onSubmit(data: z.infer<typeof emailFormSchema>) {
    setIsLoading(true)
    //模拟发送邮件
    setTimeout(() => {
      onSuccess(data.email)
      setIsLoading(false)
    }, 3000)
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn('grid gap-3', className)}
        {...props}
      >
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button className="mt-8" disabled={isLoading}>
          {isLoading ? (
            <div className="flex items-center gap-2">
              <Loader2 className="animate-spin" />
              <span>Please wait</span>
            </div>
          ) : (
            '发送登录链接'
          )}
        </Button>
      </form>
    </Form>
  )
}
