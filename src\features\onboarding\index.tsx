import React, { useState } from 'react'
import ProjectApplication from '@/features/onboarding/components/project-application.tsx'
import ApplySuccess from '@/features/onboarding/components/apply-success.tsx'
import { Button } from '@/components/ui/button.tsx'
import { useNavigate } from '@tanstack/react-router'


const Onboarding: React.FC = () => {
  const [apply, setApply] = useState(false)
  const [projectName, setProjectName] = useState('')
  const navigate = useNavigate()


  return (
    <div className="flex h-screen bg-white">
      {/* 左侧装饰 */}
      <div
        className="flex-2 flex items-end justify-center relative overflow-hidden h-full">
        <div className="absolute top-2 text-slate-100 left-1 flex items-center">
          <Button
            variant="ghost"
            className="rounded-lg transition-colors text-black"
            onClick={() => {
              navigate({ to: '/' })
            }}
          >
            返回首页
          </Button>
        </div>
        <div className="w-full h-full overflow-hidden">
          <img
            src="src/assets/onboarding.png"
            className="w-full h-full object-cover object-left"
            alt="img"
          />
        </div>
      </div>
      {/* 右侧内容区 */}
      <div className="flex-3 flex flex-col justify-center items-center px-24 ">
        {!apply ?
          (
            <ProjectApplication onApply={(project) => {
              setProjectName(project)
              setApply(true)
            }}
            />
          ) :
          (
            <ApplySuccess projectName={projectName} />
          )
        }
      </div>
    </div>
  )
}

export default Onboarding
