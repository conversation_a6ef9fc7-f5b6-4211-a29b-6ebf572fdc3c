import { Node, Edge } from '@xyflow/react'

export interface DeploymentNode extends Node {
  data: {
    label: string
    description?: string
    icon: string
    tag?: string
    nodeType?: string
  }
}

export interface HostNode extends DeploymentNode {
  data: {
    label: string
    description?: string
    icon: string
    tag?: string
    nodeType: 'host'
    ipAddress?: string
    operatingSystem?: string
    diskSpace?: string
    memory?: string
    cpuCores?: string
  }
}

export interface DeploymentEdge extends Edge {
  animated?: boolean
}

export interface DeploymentPlanData {
  nodes: DeploymentNode[]
  edges: DeploymentEdge[]
}
