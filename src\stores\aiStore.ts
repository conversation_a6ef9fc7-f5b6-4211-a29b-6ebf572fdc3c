import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'

import { FlowiseClientApi, RequestMessage } from '@/services/ai'
import { prettyObject } from '@/utils/format'
import { getSmartSelectSystemPrompt } from '@/utils/prompt.ts'
import { jsonEnd, jsonStart, jsonStartSplit, splitJson } from '@/features/apps/utils/smart-select-utils.ts'
import { FlowiseAgent } from '@/constant.ts'

export type ChatMessage = RequestMessage & {
  date: string;
  streaming?: boolean;
  isError?: boolean;
  id: number;
  isSmartSelectResp?: boolean;
  smartSelectList?: string[];
  status?: 'pending' | 'success' | 'error';
  smartSelectHandle?: boolean;
};

export function createMessage(override: Partial<ChatMessage>): ChatMessage {
  return {
    id: 1,
    date: new Date().toLocaleString(),
    role: 'userMessage',
    content: '',
    ...override,
  }
}


export interface ChatSession {
  id: string;
  topic: string;
  messages: ChatMessage[];
  createdAt: Date;
  type: 'smartSelect';
}

interface ChatStore {
  sessions: ChatSession[];
  currentSmartSelectSessionId: string | null;
  addSession: (session: ChatSession) => void;
  updateTargetSession: (
    targetSession: ChatSession,
    updater: (session: ChatSession) => void,
  ) => void;
  setCurrentSession: (sessionId: string, type: string) => void;
  getCurrentSession: (type: string) => ChatSession | null;
  getSession: (sessionId: string) => ChatSession | undefined;
  getMessagesWithMemory: (type: string) => Promise<ChatMessage[]>;
  deleteSession: (sessionId: string, type: string) => ChatSession[];
  onSmartSelectInput: (
    content: string,
  ) => Promise<void>;
}

export const useChatStore = create<ChatStore>()(
  persist(
    (set, get) => ({
      sessions: [],
      currentSmartSelectSessionId: null,
      addSession: (session: ChatSession) => {
        if (session.type === 'smartSelect') {
          set((state) => ({
            sessions: [...state.sessions, session],
            currentSmartSelectSessionId: session.id,
          }))
        }
      },
      updateTargetSession: (
        targetSession: ChatSession,
        updater: (session: ChatSession) => void,
      ) => {
        const sessions = get().sessions
        const index = sessions.findIndex((s) => s.id === targetSession.id)

        if (index < 0) return
        updater(sessions[index])
        set(() => ({ sessions }))
      },
      setCurrentSession: (sessionId: string, type: string) => {
        if (type === 'smartSelect') {
          set({ currentSmartSelectSessionId: sessionId })
        }
      },
      getCurrentSession: (type: string) => {
        let currentSession = null
        if (type === 'smartSelect') {
          currentSession = get().getSession(get().currentSmartSelectSessionId!)
        }
        if (currentSession === undefined) return null

        return currentSession
      },
      getSession: (sessionId: string) => {
        return get().sessions.find((session) => session.id === sessionId)
      },
      deleteSession: (sessionId: string, type: string) => {
        if (type === 'smartSelect' && sessionId === get().currentSmartSelectSessionId) {
          set({ currentSmartSelectSessionId: null })
        }
        set((state) => ({
          sessions: state.sessions.filter((s) => s.id !== sessionId),
        }))
        return get().sessions.filter((s) => s.type === type)
      },
      async getMessagesWithMemory(type: string) {
        const session = get().getCurrentSession(type)!
        const messages = session.messages.slice()
        const totalMessageCount = session.messages.length

        // var systemPrompts: ChatMessage[] = [];

        // short term memory
        const contextStartIndex = Math.max(
          0,
          totalMessageCount - 5,
        )

        const reversedRecentMessages = []

        for (
          let i = totalMessageCount - 1;
          i >= contextStartIndex;
          i -= 1
        ) {
          const msg = messages[i]

          if (!msg || msg.isError) continue
          reversedRecentMessages.push(msg)
        }

        // concat all messages
        return [
          ...reversedRecentMessages.reverse(),
        ]
      },

      async onSmartSelectInput(
        content: string,
      ) {
        const session = get().getCurrentSession('smartSelect')
        if (!session) return
        const messagesLength = session.messages.length
        const api: FlowiseClientApi = new FlowiseClientApi(FlowiseAgent.SmartSelectionAgent)

        const userMessage = createMessage({
          id: messagesLength! + 1,
          role: 'userMessage',
          content: content,
        })

        const botMessage = createMessage({
          id: messagesLength! + 2,
          role: 'apiMessage',
          streaming: true,
        })

        const updateMessage = [userMessage, botMessage]

        get().updateTargetSession(session, (session) => {
          session.messages = session.messages.concat(updateMessage)
        })

        const sendQuestion = JSON.stringify({
          question: content,
          prompt: getSmartSelectSystemPrompt(),
        })

        await api.flowiseAgent.chat({
          question: sendQuestion,
          chatId: session.id,
          overrideConfig: {
            sessionId: session.id,
          },
          onUpdate: (message) => {
            if (message) {
              if (botMessage.isSmartSelectResp || jsonStart(message)) {
                botMessage.isSmartSelectResp = true
                if (!botMessage.status) {
                  botMessage.content = jsonStartSplit(message)
                  botMessage.status = 'pending'
                }
                if (botMessage.status != 'pending' || jsonEnd(message)) {
                  try {
                    const { commonMessage, smartSelectList } = splitJson(message)
                    botMessage.content = commonMessage
                    botMessage.smartSelectList = smartSelectList
                    botMessage.status = 'success'
                  } catch (_) {
                    botMessage.content = message
                    botMessage.smartSelectList = []
                    botMessage.status = 'error'
                  }
                }
              } else {
                botMessage.content = message
              }
              get().updateTargetSession(session, (session) => {
                session.messages = session.messages.concat()
              })
            }
          },
          onFinish: () => {
            botMessage.streaming = false
          },
          onError: (error) => {
            const isAborted = error.message?.includes?.('aborted')

            botMessage.content +=
              '\n\n' +
              prettyObject({
                error: true,
                message: error.message,
              })
            botMessage.streaming = false
            userMessage.isError = !isAborted
            botMessage.isError = !isAborted

            get().updateTargetSession(session, (session) => {
              session.messages = session.messages.concat()
            })
          },
        })

      },
    }),
    {
      name: 'ai-storage',
      storage: createJSONStorage(() => sessionStorage),
      partialize: (state) => ({
        sessions: state.sessions,
        currentSmartSelectSessionId: state.currentSmartSelectSessionId,
      }),
    },
  ),
)
